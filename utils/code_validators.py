from __future__ import annotations

import ast
from io import StringIO
import os
import re
import subprocess
from tempfile import TemporaryDirectory
import tempfile
import traceback
from dataclasses import dataclass
from typing import Optional
import uuid
import warnings

from pylint.lint import Run
from pylint.reporters.text import TextReporter
from loguru import logger
from tree_sitter import Node, Parser, Language
from tree_sitter_languages import get_parser as tree_sitter_get_parser
import tree_sitter_python
import tree_sitter_javascript
import tree_sitter_typescript
import tree_sitter_html
import tree_sitter_css
import tree_sitter_json

from core.entities import Snippet
from logn.cache import file_cache
from utils.fuzzy_diff import patience_fuzzy_additions

warnings.simplefilter("ignore", category=FutureWarning)

AVG_CHAR_IN_LINE = 60

# 优化后的分块参数配置
DEFAULT_TARGET_LINES = 40  # 目标分块大小（行数）
MIN_CHUNK_LINES = 10  # 最小分块大小，避免过小的块
MAX_CHUNK_LINES = 80  # 最大分块大小，防止过大的块
COALESCE_THRESHOLD = 8  # 合并阈值，小于此行数的块会被合并


def get_parser(language: str):
    if language in ("python", "py"):
        lang = Language(tree_sitter_python.language())
    elif language in ("javascript", "js", "jsx"):
        lang = Language(tree_sitter_javascript.language())
    elif language in ("typescript", "ts"):
        lang = Language(tree_sitter_typescript.language_typescript())
    elif language in ("tsx"):
        lang = Language(tree_sitter_typescript.language_tsx())
    elif language == "html":
        lang = Language(tree_sitter_html.language())
    elif language == "css":
        lang = Language(tree_sitter_css.language())
    elif language == "json":
        lang = Language(tree_sitter_json.language())
    else:
        return tree_sitter_get_parser(language)
    return Parser(lang)


def non_whitespace_len(s: str) -> int:  # new len function
    return len(re.sub("\s", "", s))


def get_line_number(index: int, source_code: str) -> int:
    if index <= 0:
        return 1

    total_chars = 0
    line_number = 1

    if isinstance(source_code, bytes):
        source_code = source_code.decode("utf-8", errors="replace")

    lines = source_code.splitlines(keepends=True)

    for line_number, line in enumerate(lines, start=1):
        total_chars += len(line)
        if total_chars > index:
            return line_number

    return len(lines) if lines else 1


@dataclass
class Span:
    # Represents a slice of a string
    start: int = 0
    end: int = 0

    def __post_init__(self):
        # If end is None, set it to start
        if self.end is None:
            self.end = self.start

    def extract(self, s: str) -> str:
        # Grab the corresponding substring of string s by bytes
        return s[self.start : self.end]

    def extract_lines(self, s: str) -> str:
        # Grab the corresponding substring of string s by lines
        return "\n".join(s.splitlines()[self.start : self.end + 1])

    def __add__(self, other: Span | int) -> Span:
        # e.g. Span(1, 2) + Span(2, 4) = Span(1, 4) (concatenation)
        # There are no safety checks: Span(a, b) + Span(c, d) = Span(a, d)
        # and there are no requirements for b = c.
        if isinstance(other, int):
            return Span(self.start + other, self.end + other)
        elif isinstance(other, Span):
            return Span(self.start, other.end)
        else:
            raise NotImplementedError()

    def __len__(self) -> int:
        # i.e. Span(a, b) = b - a
        return self.end - self.start


def chunk_tree_optimized(
    tree,
    source_code: bytes,
    target_lines: int = DEFAULT_TARGET_LINES,
    min_lines: int = MIN_CHUNK_LINES,
    max_lines: int = MAX_CHUNK_LINES,
) -> list[Span]:
    """
    优化的基于AST的分块算法，以行数为主要控制单位
    优先保持语义完整性，按照语法结构进行智能分块
    """
    # 转换为字符串并按行分割
    if isinstance(source_code, bytes):
        source_code_str = source_code.decode("utf-8", errors="replace")
    else:
        source_code_str = source_code

    lines = source_code_str.splitlines()
    total_lines = len(lines)

    if total_lines == 0:
        return []

    # 如果文件很小，直接返回整个文件
    if total_lines <= max_lines:
        return [Span(1, total_lines)]

    jsx_boundaries = extract_jsx_boundaries(tree, source_code_str)
    python_boundaries = extract_python_boundaries(tree, source_code_str)

    def get_semantic_boundaries(node: Node) -> list[tuple[int, int, str]]:
        """
        获取语义边界点，包括函数、类、重要语句块等
        返回 (start_line, end_line, node_type) 的列表
        """
        boundaries = []

        # 定义重要的语义节点类型
        important_node_types = {
            "function_definition",
            "class_definition",
            "function_declaration",
            "class_declaration",
            "method_definition",
            "arrow_function",
            "if_statement",
            "for_statement",
            "while_statement",
            "try_statement",
            "interface_declaration",
            "type_alias_declaration",
            "export_statement",
            "jsx_element",
            "jsx_fragment",
            "jsx_self_closing_element",
        }

        def traverse(n):
            start_line = n.start_point[0] + 1
            end_line = n.end_point[0] + 1

            # 如果是重要节点且大小合适，记录边界
            if n.type in important_node_types:
                node_lines = end_line - start_line + 1
                if min_lines <= node_lines <= max_lines:
                    boundaries.append((start_line, end_line, n.type))
                elif node_lines > max_lines:
                    # 对于过大的节点，递归处理子节点
                    for child in n.children:
                        traverse(child)
                    return  # 不添加当前节点，只处理子节点

            # 继续处理子节点
            for child in n.children:
                traverse(child)

        traverse(node)
        return boundaries

    # 获取语义边界
    semantic_boundaries = get_semantic_boundaries(tree.root_node)

    # 按起始行排序
    semantic_boundaries.sort(key=lambda x: x[0])

    # 合并重叠和相邻的边界
    merged_boundaries = []
    for start, end, node_type in semantic_boundaries:
        if not merged_boundaries or start > merged_boundaries[-1][1] + 1:
            merged_boundaries.append((start, end, node_type))
        else:
            # 合并重叠或相邻的边界
            last_start, last_end, last_type = merged_boundaries[-1]
            merged_boundaries[-1] = (
                last_start,
                max(last_end, end),
                f"{last_type}+{node_type}",
            )

    # 填补空隙，确保完整覆盖
    final_ranges = []
    current_line = 1

    for start, end, node_type in merged_boundaries:
        # 处理空隙
        if current_line < start:
            gap_size = start - current_line

            # 检查空隙是否应该与下一个语义块合并（特别是注释）
            should_merge_gap_with_next = should_merge_gap_with_next_function(
                lines, current_line, start - 1, start
            )

            if should_merge_gap_with_next:
                # 空隙与下一个语义块合并
                start = current_line
            elif gap_size >= min_lines:
                # 空隙足够大，单独成块
                final_ranges.append((current_line, start - 1))
            elif final_ranges:
                # 空隙很小，与前一个块合并
                final_ranges[-1] = (final_ranges[-1][0], start - 1)
            else:
                # 第一个空隙，与后面的语义块合并
                start = current_line

        # 检查语义块大小
        block_size = end - start + 1
        if block_size > max_lines:
            # 语义块过大，需要进一步分割
            # 根据文件类型选择合适的分割策略
            if python_boundaries:
                sub_ranges = split_large_block_python_safe(
                    lines, start, end, target_lines, max_lines, python_boundaries
                )
            else:
                sub_ranges = split_large_block_jsx_safe(
                    lines, start, end, target_lines, max_lines, jsx_boundaries
                )
            final_ranges.extend(sub_ranges)
        else:
            final_ranges.append((start, end))

        current_line = end + 1

    # 处理最后的空隙
    if current_line <= total_lines:
        remaining_size = total_lines - current_line + 1
        if remaining_size >= min_lines:
            final_ranges.append((current_line, total_lines))
        elif final_ranges:
            # 剩余内容很少，与最后一个块合并
            final_ranges[-1] = (final_ranges[-1][0], total_lines)

    # 后处理：合并过小的块
    final_ranges = coalesce_small_chunks(final_ranges, min_lines, max_lines)

    # 修复分块边界：将开头是结束符号的行移动到上一个块
    # 根据文件类型选择合适的边界修复策略
    if python_boundaries:
        final_ranges = fix_chunk_boundaries_python_safe(
            final_ranges, lines, max_lines, python_boundaries
        )
    else:
        final_ranges = fix_chunk_boundaries_jsx_safe(
            final_ranges, lines, max_lines, jsx_boundaries
        )

    snippets = [Span(start, end) for start, end in final_ranges]

    # 过滤掉空的分段
    return filter_empty_spans(snippets, source_code_str)


def filter_empty_spans(spans: list[Span], source_code: str) -> list[Span]:
    """
    过滤掉内容为空的span（trim后为空）
    """
    filtered_spans = []
    lines = source_code.splitlines()

    for span in spans:
        # 获取span对应的内容
        start_line = max(1, span.start)
        end_line = min(len(lines), span.end)

        if start_line <= end_line:
            content_lines = lines[start_line - 1 : end_line]
            content = "\n".join(content_lines).strip()

            # 只有内容不为空时才保留
            if content:
                filtered_spans.append(span)

    return filtered_spans


def split_large_block(
    lines: list, start_line: int, end_line: int, target_lines: int, max_lines: int
) -> list[tuple[int, int]]:
    """
    分割过大的代码块，尽量在语义边界处分割
    """
    ranges = []
    current_start = start_line

    while current_start <= end_line:
        remaining_lines = end_line - current_start + 1

        if remaining_lines <= max_lines:
            # 剩余部分不大，直接作为一个块
            ranges.append((current_start, end_line))
            break

        # 寻找合适的分割点
        ideal_end = current_start + target_lines - 1
        actual_end = find_good_split_point_enhanced(
            lines,
            current_start,
            ideal_end,
            min(current_start + max_lines - 1, end_line),
        )

        ranges.append((current_start, actual_end))
        current_start = actual_end + 1

    return ranges


def find_good_split_point_enhanced(
    lines: list, start_line: int, ideal_end: int, max_end: int
) -> int:
    """
    增强的分割点查找，考虑更多语义因素
    简单优化JavaScript数组和对象的分割
    """
    # 搜索范围：理想位置前后的范围
    search_start = max(start_line, ideal_end - 10)
    search_end = min(max_end, ideal_end + 15)

    best_split = ideal_end
    best_priority = 0

    # 检查是否有函数结束标记
    for line_num in range(search_start, search_end + 1):
        if line_num >= len(lines):
            break

        line = lines[line_num - 1].strip()  # 转换为0-based索引
        priority = 0

        # 优先级评分系统
        if line == "}" or line in ["})", "};", "},"]:
            # 函数/类结束
            priority = 10
        elif line == "}," or line.endswith("},"):
            # 对象项结束，很好的分割点
            priority = 9
        elif line == "" and line_num < len(lines) - 1:
            next_line = lines[line_num].strip() if line_num < len(lines) else ""
            if next_line.startswith(
                ("def ", "class ", "function ", "const ", "let ", "var ", "export ")
            ):
                # 空行后跟新的定义
                priority = 9
            else:
                # 普通空行
                priority = 6
        elif line.startswith(("def ", "class ", "function ", "export ", "import ")):
            # 新的定义开始（在此之前分割）
            priority = 8
            best_split = line_num - 1  # 在定义之前分割
        elif line.endswith((";", ":")):
            # 语句结束
            priority = 5
        elif line.startswith(("# ", "// ", "/* ", "* ", "/**")):
            # 注释行
            priority = 3
        elif line.endswith(",") and not line.endswith('",') and not line.endswith("',"):
            # 列表/参数继续，但对于对象项给予更高优先级
            if ":" in line:  # 可能是对象属性
                priority = 4
            else:
                priority = 2

        # 选择最佳分割点
        if priority > best_priority or (
            priority == best_priority
            and abs(line_num - ideal_end) < abs(best_split - ideal_end)
        ):
            best_split = line_num
            best_priority = priority

    return best_split


def should_merge_gap_with_next_function(
    lines: list[str], gap_start: int, gap_end: int, next_start: int
) -> bool:
    """
    判断空隙是否应该与下一个语义块合并
    特别处理注释与函数的关系
    """
    # 检查空隙中是否包含注释
    has_comment = False
    for line_idx in range(gap_start - 1, gap_end):
        if line_idx >= len(lines):
            break
        line = lines[line_idx].strip()
        if line.startswith("//") or line.startswith("#") or line.startswith("/*"):
            has_comment = True
            break

    if not has_comment:
        return False

    # 检查下一个语义块是否是函数定义
    if next_start <= len(lines):
        next_line = lines[next_start - 1].strip()
        if (
            next_line.startswith(
                ("function", "const", "let", "var", "export", "class", "def")
            )
            or "=" in next_line
            or next_line.endswith("{")
        ):
            return True

    return False


def coalesce_small_chunks(
    ranges: list[tuple[int, int]], min_lines: int, max_lines: int
) -> list[tuple[int, int]]:
    """
    合并过小的代码块
    """
    if not ranges:
        return ranges

    coalesced = []

    for start, end in ranges:
        chunk_size = end - start + 1

        if chunk_size < min_lines and coalesced:
            # 当前块太小，尝试与前一个块合并
            prev_start, prev_end = coalesced[-1]
            merged_size = end - prev_start + 1

            if merged_size <= max_lines:
                # 合并后不会太大，执行合并
                coalesced[-1] = (prev_start, end)
            else:
                # 合并后会太大，保持原样
                coalesced.append((start, end))
        else:
            coalesced.append((start, end))

    return coalesced


def fix_chunk_boundaries(
    ranges: list[tuple[int, int]], lines: list[str], max_lines: int
) -> list[tuple[int, int]]:
    """
    修复分块边界问题：如果某个分块的开始都是结束符号（}、)、,等），
    将这些行移动到上一个分块中，直到遇到有意义的内容
    同时处理单行注释与代码分离的问题
    """
    if not ranges or len(ranges) < 2:
        return ranges

    # 定义结束符号 - 扩展检测范围
    ending_symbols = {"}", ")", ",", ";", "};", "},", "})", "});}", "),", ");", "});"}

    fixed_ranges = []

    for i, (start, end) in enumerate(ranges):
        if i == 0:
            # 第一个块不需要修复
            fixed_ranges.append((start, end))
            continue

        # 检查当前块开头的行是否都是结束符号
        current_start = start
        lines_to_move = 0

        # 从当前块的开始往下扫描，找到所有连续的结束符号行和空行
        for line_idx in range(start - 1, min(end, start + 10)):  # 最多检查10行
            if line_idx >= len(lines):
                break

            line = lines[line_idx].strip()

            # 跳过空行
            if not line:
                lines_to_move += 1
                continue

            # 检查是否是单行注释（应该与后续代码合并）
            if line.startswith("//") or line.startswith("#"):
                # 检查这个注释是否应该与后面的代码合并
                should_merge_comment = False

                # 查看注释后面几行是否有相关代码
                for check_idx in range(line_idx + 1, min(len(lines), line_idx + 5)):
                    check_line = lines[check_idx].strip()
                    if not check_line:
                        continue
                    # 如果注释后面紧跟着函数、变量定义等，应该合并
                    if (
                        check_line.startswith(
                            (
                                "function",
                                "const",
                                "let",
                                "var",
                                "export",
                                "class",
                                "def",
                            )
                        )
                        or "=" in check_line
                        or check_line.endswith("{")
                    ):
                        should_merge_comment = True
                        break
                    # 如果遇到其他注释，继续检查
                    elif check_line.startswith(("//", "#", "/*")):
                        continue
                    else:
                        # 遇到其他代码，也应该合并
                        should_merge_comment = True
                        break

                if should_merge_comment:
                    lines_to_move += 1
                else:
                    # 不应该合并，停止移动
                    break
                continue

            # 检查是否是结束符号行（更宽松的检测）
            is_ending_line = False

            # 直接匹配已知的结束符号
            if line in ending_symbols:
                is_ending_line = True
            # 检查是否完全由结束符号组成
            elif all(c in "}),;" or c.isspace() for c in line):
                is_ending_line = True
            # 检查是否以结束符号开头且后面只有简单内容
            elif line.startswith(("}", ")", ",", ";")) and (
                len(line) <= 3 or line.endswith((",", ";", "}", ")"))
            ):
                is_ending_line = True

            if is_ending_line:
                lines_to_move += 1
            else:
                # 遇到有意义的内容，停止
                break

        if lines_to_move > 0:
            # 需要移动一些行到上一个块
            prev_start, prev_end = fixed_ranges[-1]
            new_prev_end = prev_end + lines_to_move
            new_current_start = current_start + lines_to_move

            # 检查移动后上一个块是否会太大
            prev_new_size = new_prev_end - prev_start + 1
            if prev_new_size <= max_lines and new_current_start <= end:
                # 可以安全移动
                fixed_ranges[-1] = (prev_start, new_prev_end)
                if new_current_start <= end:
                    fixed_ranges.append((new_current_start, end))
                # 如果新的当前块为空，不添加
            else:
                # 移动后会太大，保持原样
                fixed_ranges.append((start, end))
        else:
            # 不需要移动
            fixed_ranges.append((start, end))

    return fixed_ranges


def chunk_tree(
    tree,
    source_code: bytes,
    MAX_CHARS=AVG_CHAR_IN_LINE * 200,  # 200 lines of code
    coalesce=AVG_CHAR_IN_LINE * 50,  # 50 lines of code
) -> list[Span]:
    """
    重新设计的分块算法，直接基于行号工作，避免字节索引转换的问题
    保持向后兼容性，但推荐使用 chunk_tree_optimized
    """
    # 使用优化的分块算法
    target_lines = MAX_CHARS // AVG_CHAR_IN_LINE
    coalesce_lines = max(COALESCE_THRESHOLD, coalesce // AVG_CHAR_IN_LINE)

    return chunk_tree_optimized(
        tree,
        source_code,
        target_lines=min(target_lines, DEFAULT_TARGET_LINES),
        min_lines=coalesce_lines,
        max_lines=max(target_lines, MAX_CHUNK_LINES),
    )


extension_to_language = {
    "js": "tsx",
    "jsx": "tsx",
    "ts": "tsx",
    "tsx": "tsx",
    "mjs": "tsx",
    "sjs": "tsx",
    "py": "python",
    "rs": "rust",
    "go": "go",
    "java": "java",
    "cpp": "cpp",
    "cc": "cpp",
    "cxx": "cpp",
    "c": "cpp",
    "h": "cpp",
    "hpp": "cpp",
    "cs": "cpp",
    "rb": "ruby",
    "md": "markdown",
    "rst": "markdown",
    "txt": "markdown",
    # "erb": "embedded-template",
    # "ejs": "embedded-template",
    # "html": "embedded-template",
    "erb": "html",
    "ejs": "html",
    "html": "html",
    "xhsml": "html",
    "wxml": "html",
    "vue": "html",
    "css": "css",
    "json": "json",
    "php": "php",
    "elm": "elm",
}


def naive_chunker(code: str, line_count: int = 30, overlap: int = 15):
    if overlap >= line_count:
        raise ValueError("Overlap should be smaller than line_count.")
    lines = code.split("\n")
    total_lines = len(lines)
    chunks = []

    start = 0
    while start < total_lines:
        end = min(start + line_count, total_lines)
        chunk = "\n".join(lines[start:end])
        chunks.append(chunk)

        # Calculate next start position, ensuring we don't skip lines
        next_start = start + line_count - overlap

        # If next_start would be >= total_lines, we're done
        if next_start >= total_lines:
            break

        # If the remaining lines are less than line_count, include them in the last chunk
        remaining_lines = total_lines - next_start
        if remaining_lines < line_count and remaining_lines > 0:
            # Extend the last chunk to include all remaining lines
            final_chunk = "\n".join(lines[next_start:total_lines])
            if final_chunk.strip():  # Only add if there's actual content
                chunks.append(final_chunk)
            break

        start = next_start

    return chunks


def chunk_markdown(code: str, max_lines: int = 80) -> list[str]:
    """
    专门用于markdown文件的分段函数
    按照标题层级进行智能分段，保持语义完整性
    使用更粗粒度的分块策略
    """
    lines = code.split("\n")
    chunks = []
    current_chunk = []

    for i, line in enumerate(lines):
        # 检查是否是标题行
        if line.strip().startswith("#"):
            # 计算标题级别
            header_level = len(line.strip()) - len(line.strip().lstrip("#"))

            # 如果当前chunk不为空，考虑是否需要分块
            if current_chunk:
                # 对于一级标题，总是分块
                # 对于二级标题，如果当前chunk已经有一定长度，则分块
                # 对于三级及以下标题，只有在chunk很长时才分块
                should_split = False

                if header_level == 1:
                    should_split = True
                elif header_level == 2 and len(current_chunk) >= 20:
                    should_split = True
                elif header_level >= 3 and len(current_chunk) >= max_lines * 0.8:
                    should_split = True

                if should_split:
                    chunks.append("\n".join(current_chunk))
                    current_chunk = []

            # 开始新的chunk，包含标题
            current_chunk.append(line)
        else:
            current_chunk.append(line)

            # 如果当前chunk达到最大行数，寻找合适的分割点
            if len(current_chunk) >= max_lines:
                # 查看后面是否有标题，优先在标题前分段
                next_header_found = False
                next_header_distance = float("inf")

                for j in range(i + 1, min(i + 30, len(lines))):
                    if lines[j].strip().startswith("#"):
                        next_header_found = True
                        next_header_distance = j - i
                        break

                # 如果找到了下一个标题且距离合理，等到标题前分段
                if next_header_found and next_header_distance <= 20:
                    continue
                # 如果chunk太长了，强制分段
                elif len(current_chunk) >= max_lines * 1.3:
                    chunks.append("\n".join(current_chunk))
                    current_chunk = []

    # 添加最后的chunk
    if current_chunk:
        chunks.append("\n".join(current_chunk))

    # 后处理：合并过小的chunks
    final_chunks = []
    min_chunk_size = 15  # 提高最小chunk大小

    for chunk in chunks:
        chunk_lines = chunk.split("\n")
        if len(chunk_lines) < min_chunk_size and final_chunks:
            # 与前一个chunk合并
            final_chunks[-1] = final_chunks[-1] + "\n" + chunk
        else:
            final_chunks.append(chunk)

    return final_chunks


def chunk_markdown_to_snippets(code: str, path: str) -> list[Snippet]:
    """
    将markdown文件转换为Snippet列表
    使用更粗粒度的分块策略以保持语义完整性
    """
    chunks = chunk_markdown(code, max_lines=80)
    snippets = []
    current_line = 1

    for chunk in chunks:
        chunk_lines = chunk.split("\n")
        chunk_line_count = len(chunk_lines)

        snippet = Snippet(
            content=code,
            start=current_line,
            end=current_line + chunk_line_count - 1,
            file_path=path,
        )
        snippets.append(snippet)
        current_line += chunk_line_count

    # 过滤掉空的snippet
    return filter_empty_snippets(snippets)


def get_new_lint_errors_for_pylint(new_errors: str, old_errors: str) -> str:
    # Example of error: "main.py:1585:12: W0612: Unused variable 'command' (unused-variable)"
    additional_errors = patience_fuzzy_additions(old_errors, new_errors).splitlines()
    old_error_types = []
    for line in old_errors.splitlines():
        if line.count(" ") > 2:
            _file_delimiter, error_type, *_ = line.split(" ")
            old_error_types.append(error_type)
    results = []
    try:
        for line in additional_errors:
            if (
                line.count(" ") >= 2
            ):  # sometimes the error doesn't have enough spaces, which raises an error
                _file_delimiter, error_type, *_ = line.split(" ")
            else:
                _file_delimiter, error_type = line.split(" ")
            if (
                error_type.startswith("E") or old_error_types.count(error_type) < 2
            ):  # if there are more than 1 of the same error, we consider it new
                results.append(line)
    except Exception as e:
        logger.info(f"Error in get_new_lint_errors_for_pylint: {e}")
    return "\n".join(results)


def get_new_lint_errors_for_eslint(new_errors: str, old_errors: str) -> str:
    # Example of error: "main.py:1585:12: W0612: Unused variable 'command' (unused-variable)"
    additional_errors = patience_fuzzy_additions(old_errors, new_errors).splitlines()
    old_error_types = []
    for line in old_errors.splitlines():
        if line.count(" ") > 2:
            *_, error_type = line.split(" ")
            old_error_types.append(error_type)
    results = []
    for line in additional_errors:
        *_, error_type = line.split(" ")
        if (
            not line.startswith("✖") and old_error_types.count(error_type) < 2
        ):  # if there are more than 1 of the same error, we consider it new
            results.append(line)
    return "\n".join(results)


@dataclass
class CheckResults:
    # Experimental feature, we'll see how this does.
    # TODO: smart parsing
    parse_error_message: str = ""
    pylint: str = ""
    eslint: str = ""

    def is_worse_than(self, other: CheckResults) -> bool:
        if self.parse_error_message:
            return True
        if other.parse_error_message:
            return False
        return len(self.pylint.splitlines()) > len(other.pylint.splitlines()) or len(
            self.eslint.splitlines()
        ) > len(other.eslint.splitlines())

    def is_worse_than_message(self, other: CheckResults) -> str:
        if other.parse_error_message:
            # Previously failing
            return ""
        if self.parse_error_message:
            return self.parse_error_message
        if len(self.pylint.splitlines()) > len(other.pylint.splitlines()):
            # return f"The code has the following pylint errors:\n\n{self.pylint}"
            new_pylint_errors = get_new_lint_errors_for_pylint(
                self.pylint, other.pylint
            )
            if not other.pylint:
                return f"The code has the following pylint errors:\n\n{self.pylint}"
            elif not new_pylint_errors:
                # All the errors are invalid
                return ""
            return (
                f"The following new pylint errors have appeared:\n\n{new_pylint_errors}"
            )
        if len(self.eslint.splitlines()) > len(other.eslint.splitlines()):
            new_eslint_errors = get_new_lint_errors_for_eslint(
                self.eslint, other.eslint
            )
            if not other.eslint:
                return f"The code has the following eslint errors:\n\n{self.eslint}"
            elif not new_eslint_errors:
                # All the errors are invalid
                return ""
            return (
                f"The following new eslint errors have appeared:\n\n{new_eslint_errors}"
            )
        return ""


def strip_ansi_codes(text: str) -> str:
    # ANSI escape sequences (color codes) are often starting with ESC ([) followed by some numbers and ends with "m".
    ansi_escape = re.compile(r"(\x9B|\x1B\[)[0-?]*[ -\/]*[@-~]")
    return ansi_escape.sub("", text)


def check_valid_typescript(file_path: str, code: str) -> tuple[bool, str]:
    is_valid = True
    message = ""
    version_check = ["tsc", "--version"]
    result = subprocess.run(
        " ".join(version_check),
        capture_output=True,
        text=True,
        shell=True,
    )
    # only run if tsc is available
    if result.returncode == 0:
        # Create a temporary file to hold the TypeScript code
        with tempfile.NamedTemporaryFile(suffix=".ts", delete=False) as temp_file:
            temp_file_path = temp_file.name
            temp_file.write(code.encode("utf-8"))

        # Run `tsc` on the temporary file
        try:
            commands = ["tsc", "--pretty", "--noEmit", temp_file_path]
            result = subprocess.run(
                " ".join(commands), shell=True, text=True, capture_output=True
            )

            if result.returncode != 0:
                message = strip_ansi_codes(result.stdout)
                index = message.index(temp_file_path)
                full_temp_file_path = message[: index + len(temp_file_path)]
                message = message.replace(full_temp_file_path, file_path)

                # import error is TS2307 and should come up after the syntax check
                import_error = "error TS2307"
                if import_error in message:
                    num_of_errors = message.count(import_error)
                    # see if this matches the total amount of errors:
                    total_error_message = f"Found {num_of_errors} error"
                    if total_error_message in message:
                        # if we only have import errors, we consider it a successful check
                        return True, ""
                    else:
                        # there are more errors than just import errors
                        # now attempt to parse the message so that we remove import errors
                        message_lines = message.split("\n")
                        while num_of_errors > 0:
                            for line in message_lines:
                                if import_error in line:
                                    message_lines = message_lines[5:]
                                    num_of_errors -= 1
                        message = "\n".join(message_lines)
                return False, message
        finally:
            # Clean up: remove the temporary file
            os.remove(temp_file_path)
    return is_valid, message


def check_syntax(file_path: str, code: str) -> tuple[bool, str]:
    ext = file_path.split(".")[-1]
    if ext in extension_to_language:
        language = extension_to_language[ext]
    else:
        return True, "Unsupported file extension, skipping syntax check."
    parser = get_parser(language)
    tree = parser.parse(code.encode("utf-8"))

    if language == "python":
        # First check for syntax errors
        try:
            ast.parse(code)
        except SyntaxError as e:
            error_message = f"Python syntax error: {e.msg} at line {e.lineno}"
            return False, error_message

    # we can't do this right now unfortunately as we don't have a way to mimic the production env for the code
    # if ext in ["ts"]:
    #     return check_valid_typescript(file_path, code)

    def find_deepest_error(node: Node) -> Optional[Node]:
        deepest_error = None
        if node.has_error:
            deepest_error = node
        for child in node.children:
            child_error = find_deepest_error(child)
            if child_error:
                deepest_error = child_error
        return deepest_error

    error_location = find_deepest_error(tree.root_node)
    if error_location:
        start_line, start_col = error_location.start_point
        end_line, end_col = error_location.end_point
        code_lines = code.split("\n")
        surrounding_lines = 3
        error_code_lines = code_lines[
            max(0, start_line - surrounding_lines) : start_line
        ]
        if start_line == end_line:
            error_code_lines += [code_lines[start_line]]
            error_code_lines += [" " * start_col + "^" * max(end_col - start_col, 1)]
        else:
            error_code_lines += ["=== ERROR START ==="]
            error_code_lines += code_lines[start_line : end_line + 1]
            error_code_lines += ["=== ERROR END ==="]
        error_code_lines += code_lines[
            end_line + 1 : min(len(code_lines) - 1, end_line + surrounding_lines)
        ]
        error_span = "\n".join(error_code_lines)
        if start_line == end_line:
            error_message = f"Invalid syntax found at line {start_line}, displayed below:\n{error_span}"
        else:
            error_message = f"Invalid syntax found from {start_line}-{end_line}, displayed below:\n{error_span}"
        return (False, error_message)
    return True, ""


# Need to add "no-unused-vars": "error"
# Need to add "import/first": "error"

DEFAULT_ESLINTRC = """{
    "parser": "@typescript-eslint/parser",
    "parserOptions": {
      "ecmaVersion": 2020,
      "sourceType": "module",
      "ecmaFeatures": {
        "jsx": true
      }
    },
    "settings": {
      "react": {
        "version": "detect"
      }
    },
    "extends": [
      "eslint:recommended",
      "plugin:react/recommended",
      "plugin:@typescript-eslint/recommended"
    ],
    "env": {
      "browser": true,
      "es2021": true,
      "node": true
    },
    "plugins": [
      "react",
      "@typescript-eslint"
    ],
    "rules": {
        "no-undef": "error",
        "no-const-assign": "error",
        "no-redeclare": "error",
        "no-unused-vars": "error",
        "no-use-before-define": ["error", { "functions": true, "classes": true, "variables": true }],
        "import/first": "error"
    }
  }
  """

pylint_args_non_last_fcr = [
    "--disable=C",
    "--enable=C0413",  # Enable only the check for imports not at the top
    "--disable=W0611",  # Don't check unused import
    "--disable=R",
    "--disable=import-error",
    "--disable=no-member",
]

# add a comment to all lines which are changed
pylint_args_last_fcr = [
    "--disable=C",
    "--enable=C0413",
    "--enable=W0611",  # Check unused import
    "--disable=R",
    "--disable=import-error",
    "--disable=no-member",
]


@file_cache()
def get_pylint_check_results(
    file_path: str, code: str, last_fcr_for_file=False
) -> CheckResults:
    logger.debug(f"Running pylint on {file_path}...")
    file_hash = uuid.uuid4().hex
    new_file = os.path.join("/tmp", file_hash + "_" + os.path.basename(file_path))
    stem = os.path.splitext(os.path.basename(file_path))[0]
    with open(new_file, "w") as f:
        f.write(code)
    pylint_output = StringIO()
    reporter = TextReporter(pylint_output)
    # this allows us to have a more rigorous check for the last file change request
    pylint_args = [new_file] + (
        pylint_args_last_fcr if last_fcr_for_file else pylint_args_non_last_fcr
    )
    Run(
        pylint_args,
        reporter=reporter,
        exit=False,
    )
    error_message = pylint_output.getvalue().strip()
    try:
        os.remove(new_file)
    except FileNotFoundError:
        pass
    succeeded = error_message.startswith("------------------------------------")
    if error_message:
        error_message = error_message.replace(new_file, file_path).replace(
            f"{file_hash}_" + stem, stem
        )
        error_message = error_message.split("-----------------------------------", 1)[
            0
        ].strip()
        error_message = f"> pylint {file_path}\n\n" + error_message
    logger.debug("Done running pylint.")
    return CheckResults(pylint=error_message if not succeeded else "")


def get_check_results(
    file_path: str, code: str, last_fcr_for_file=False
) -> CheckResults:
    is_valid, error_message = check_syntax(file_path, code)
    if not is_valid:
        return CheckResults(parse_error_message=error_message)
    ext = file_path.rsplit(".")[-1]  # noqa
    if ext == "py":
        try:
            return get_pylint_check_results(
                file_path, code, last_fcr_for_file=last_fcr_for_file
            )
        except Exception as e:
            logger.exception(e)
    elif ext in ["js", "jsx", "ts", "tsx"]:
        # see if eslint is installed
        npx_commands = ["npx", "eslint", "--version"]
        try:
            result = subprocess.run(
                " ".join(npx_commands),
                timeout=5,
                capture_output=True,
                text=True,
                shell=True,
            )
        except subprocess.TimeoutExpired:
            raise Exception(
                "ESLint timed out after 5s. You need eslint to edit js/ts files. Run `npm i -g eslint @typescript-eslint/parser @typescript-eslint/eslint-plugin eslint-plugin-import eslint-plugin-react`."
            )
        # Check eslint < v9 and all the plugins exist
        if result.returncode == 0:
            with TemporaryDirectory(dir=os.getcwd()) as temp_dir:
                file_name = file_path.split(os.path.sep)[-1]
                new_file = os.path.join(temp_dir, f"{file_name}")
                config_file = os.path.join(temp_dir, ".eslintrc")
                with open(config_file, "w") as f:
                    f.write(DEFAULT_ESLINTRC)
                with open(new_file, "w") as f:
                    f.write(code)
                try:
                    eslint_commands = [
                        "npx",
                        "eslint",
                        new_file,
                        "--config",
                        config_file,
                        "--no-ignore",
                    ]
                    result = subprocess.run(
                        " ".join(eslint_commands),
                        capture_output=True,
                        text=True,
                        shell=True,
                        timeout=30,
                    )
                    error_message = (
                        (result.stdout + "\n\n" + result.stderr)
                        .strip()
                        .replace(new_file, file_path)
                    )
                    return CheckResults(eslint=error_message)
                except subprocess.TimeoutExpired:
                    logger.warning(f"ESLint timed out after 30s for {file_path}")
                    pass
    return CheckResults()


PRETTIERRC_FILES = [
    ".prettierrc",
    ".prettierrc.json",
    ".prettierrc.yaml",
    ".prettierrc.yml",
    ".prettierrc.js",
    "prettier.config.js",
]


def format_file(file_path: str, code: str, cwd: str | None = None) -> str:
    """
    Currently only supports JavaScript, TypeScript, and JSX.
    """
    file_name, ext = os.path.splitext(file_path)
    ext = ext.removeprefix(".")
    if ext in ("js", "jsx", "ts", "tsx"):
        prettier_config_path, prettier_config_contents = None, None
        for prettierrc_file in PRETTIERRC_FILES:
            full_path = os.path.join(cwd, prettierrc_file)
            if os.path.exists(full_path):
                # shutil.copy2(os.path.join(cwd, prettierrc_file), temp_dir)
                prettier_config_path = prettierrc_file
                with open(full_path, "r") as f:
                    prettier_config_contents = f.read()
                break

        if not prettier_config_path or not prettier_config_contents:
            return code

        with TemporaryDirectory(dir=os.getcwd()) as temp_dir:
            """
            Check if there is a prettierrc file in the current directory and copy it to the temp directory.
            If there is no prettierrc file, return the original code.
            """
            with open(os.path.join(temp_dir, prettierrc_file), "w") as f:
                f.write(prettier_config_contents)
            npx_commands = ["npx", "prettier", "--stdin-filepath", file_path]
            try:
                result = subprocess.run(
                    " ".join(npx_commands),
                    input=code,
                    capture_output=True,
                    text=True,
                    shell=True,
                    cwd=temp_dir,
                )
                if result.returncode != 0:
                    logger.error(result.stderr)
                    return code
                return result.stdout
            except Exception as e:
                logger.error(e)
                return code
    return code


# @file_cache()
def chunk_code(
    code: str,
    path: str,
    MAX_CHARS=AVG_CHAR_IN_LINE * 200,  # 200 lines of code - 保持向后兼容
    coalesce=80,
    is_extension_source: bool = False,  # 标识是否为extension_sources处理
    enable_contextual_retrieval: bool = False,  # 是否启用上下文检索
) -> list[Snippet]:
    """
    优化的代码分块函数
    1. 优先使用函数级分块（适用于所有支持的语言）
    2. 基于40行目标大小进行智能分块
    3. 保持语义完整性
    4. 智能组合分块
    5. JSX属性完整性保护
    """
    # 如果是extension_sources处理，使用专门的处理逻辑
    if is_extension_source:
        return chunk_code_for_extension_sources(code, path)

    ext = path.split(".")[-1]
    if ext in extension_to_language:
        language = extension_to_language[ext]
    else:
        # Fallback to naive chunking if tree_sitter fails
        line_count = DEFAULT_TARGET_LINES
        overlap = 0
        chunks = naive_chunker(code, line_count, overlap)
        return create_snippets_from_chunks(chunks, code, path)

    # 专门处理markdown文件
    if language == "markdown":
        return chunk_markdown_to_snippets(code, path)

    # 专门处理CSS文件
    if language == "css":
        return chunk_css_with_tree_sitter(code, path)

    # 专门处理JSON文件
    if language == "json":
        return chunk_json_with_tree_sitter(code, path)

    # 专门处理HTML文件（包括XHSML）
    if language == "html":
        return chunk_html_with_tree_sitter(code, path)

    # 尝试使用函数级分块（适用于所有语言）
    try:
        function_snippets = chunk_code_by_function_universal(code, path)
        if function_snippets:
            # 应用智能组合策略
            combined_snippets = combine_snippets_intelligently(
                function_snippets, code, path
            )
            if ext in ["tsx", "jsx", "ts", "js", "sjs", "xhsml"]:
                combined_snippets = fix_jsx_attribute_boundaries(
                    combined_snippets, code, path
                )
            elif ext == "py":
                combined_snippets = fix_python_syntax_boundaries(
                    combined_snippets, code, path
                )
            return combined_snippets
    except Exception as e:
        logger.warning(f"Function-level chunking failed for {path}: {e}")
        # 继续使用传统方法

    # 使用优化的AST分块作为后备方案
    try:
        parser = get_parser(language)
        tree = parser.parse(code.encode("utf-8"))

        # 使用优化的分块算法
        chunks = chunk_tree_optimized(tree, code.encode("utf-8"))

        snippets = []
        lines = code.split("\n")
        total_lines = len(lines)

        for chunk in chunks:
            # Ensure valid line ranges
            start_line = max(1, chunk.start)
            end_line = min(chunk.end, total_lines)

            # Skip empty chunks
            if start_line >= end_line:
                continue

            new_snippet = Snippet(
                content=code,
                start=start_line,
                end=end_line,
                file_path=path,
            )
            snippets.append(new_snippet)

        # If no snippets were created, create one for the entire file
        if not snippets:
            snippets.append(
                Snippet(
                    content=code,
                    start=1,
                    end=total_lines,
                    file_path=path,
                )
            )

        # 过滤掉空的snippet
        snippets = filter_empty_snippets(snippets)

        if ext in ["tsx", "jsx", "ts", "js", "sjs", "xhsml"]:
            snippets = fix_jsx_attribute_boundaries(snippets, code, path)
        elif ext == "py":
            snippets = fix_python_syntax_boundaries(snippets, code, path)

        return snippets
    except Exception:
        logger.error(f"AST chunking failed for {path}: {traceback.format_exc()}")
        # Final fallback to creating a single snippet for the entire file
        lines = code.split("\n")
        snippet = Snippet(
            content=code,
            start=1,
            end=len(lines),
            file_path=path,
        )
        # 检查内容是否为空
        if snippet.get_snippet(add_ellipsis=False, add_lines=False).strip():
            return [snippet]
        else:
            return []


def filter_empty_snippets(snippets: list[Snippet]) -> list[Snippet]:
    """
    过滤掉内容为空的snippet（trim后为空）
    """
    filtered_snippets = []
    for snippet in snippets:
        # 获取snippet的内容
        content = snippet.get_snippet(add_ellipsis=False, add_lines=False)
        # 检查trim后是否为空
        if content.strip():
            filtered_snippets.append(snippet)
    return filtered_snippets


def create_snippets_from_chunks(
    chunks: list[str], code: str, path: str
) -> list[Snippet]:
    """
    从字符串块列表创建Snippet对象
    """
    snippets = []
    lines = code.split("\n")
    total_lines = len(lines)
    current_line = 1

    for chunk in chunks:
        chunk_lines = chunk.split("\n")
        chunk_line_count = len(chunk_lines)

        # Calculate end line, ensuring we don't exceed total lines
        end_line = min(current_line + chunk_line_count - 1, total_lines)

        new_snippet = Snippet(
            content=code,
            start=current_line,
            end=end_line,
            file_path=path,
        )
        snippets.append(new_snippet)

        # Move to next chunk start position
        current_line += chunk_line_count

    # 过滤掉空的snippet
    return filter_empty_snippets(snippets)


def chunk_code_by_function_universal(code: str, path: str) -> list[Snippet]:
    """
    通用的函数级代码分块，支持多种编程语言
    基于AST进行函数、方法和类定义的分块
    """
    ext = path.split(".")[-1]
    if ext not in extension_to_language:
        return []

    language = extension_to_language[ext]

    # 获取函数节点（扩展到更多语言）
    function_nodes = get_function_nodes_universal(path, code)
    lines = code.split("\n")
    total_lines = len(lines)

    # 如果没有找到函数节点，返回空列表（让调用者使用其他策略）
    if not function_nodes:
        return []

    # 提取文件的import和export信息
    import_export_info = extract_import_export_info(path, code)

    # 按照起始位置排序并去重
    if function_nodes is None:
        function_nodes = []
    function_nodes = deduplicate_function_nodes(function_nodes)

    # 创建函数级的snippet
    snippets = create_function_snippets(
        function_nodes, code, path, import_export_info, total_lines
    )

    # 过滤掉空的snippet
    return filter_empty_snippets(snippets)


def get_function_nodes_universal(file_name: str, code: str) -> list[dict]:
    """
    获取文件中所有函数、方法和类定义的节点（支持更多语言）
    """
    ext = file_name.split(".")[-1]
    if ext not in extension_to_language:
        return []

    language = extension_to_language[ext]

    # 扩展的语言节点类型映射
    type_mapping_per_language = {
        "python": [
            "function_definition",
            "class_definition",
            "async_function_definition",
            "decorated_definition",
            "with_statement",
            "try_statement",
            "if_statement",
            "for_statement",
            "while_statement",
            "match_statement",
            "import_statement",
            "import_from_statement",
            "expression_statement",
        ],
        "typescript": [
            "function_declaration",
            "class_declaration",
            "interface_declaration",
            "type_alias_declaration",
            "method_definition",
            "arrow_function",
            "export_statement",
            "lexical_declaration",
            "variable_declaration",
        ],
        "tsx": [
            "function_declaration",
            "class_declaration",
            "interface_declaration",
            "type_alias_declaration",
            "method_definition",
            "arrow_function",
            "export_statement",
            "lexical_declaration",
            "variable_declaration",
        ],
        "javascript": [
            "function_declaration",
            "class_declaration",
            "method_definition",
            "arrow_function",
            "export_statement",
            "lexical_declaration",
            "variable_declaration",
        ],
        "rust": [
            "function_item",
            "impl_item",
            "struct_item",
            "enum_item",
            "trait_item",
        ],
        "go": ["function_declaration", "type_declaration", "method_declaration"],
        "java": ["method_declaration", "class_declaration", "interface_declaration"],
        "cpp": ["function_definition", "class_specifier", "function_declarator"],
    }

    function_type_strings = type_mapping_per_language.get(language, [])
    if not function_type_strings:
        return []

    try:
        parser = get_parser(language)
        tree = parser.parse(bytes(code, "utf8"))
        function_nodes = []

        def traverse_node(node, parent=None):
            if node.type in function_type_strings:
                node_info = {
                    "type": node.type,
                    "start_point": node.start_point,
                    "end_point": node.end_point,
                    "start_byte": node.start_byte,
                    "end_byte": node.end_byte,
                    "parent": parent,
                }

                # 尝试提取名称
                name = extract_node_name(node, language)
                if name:
                    node_info["name"] = name

                function_nodes.append(node_info)

            # 递归处理子节点
            for child in node.children:
                traverse_node(child, node)

        traverse_node(tree.root_node)
        return function_nodes

    except Exception as e:
        logger.warning(f"Failed to parse {file_name} with tree-sitter: {e}")
        return []


def extract_node_name(node, language: str) -> str:
    """
    从AST节点中提取名称，支持多种语言
    """
    try:
        # 通用的名称提取逻辑
        name_node = node.child_by_field_name("name")
        if name_node:
            return name_node.text.decode("utf8")

        # 特殊处理箭头函数等
        if node.type == "arrow_function" and node.parent:
            parent = node.parent
            if parent.type in ["assignment_expression", "pair", "variable_declarator"]:
                name_node = parent.child_by_field_name(
                    "left"
                ) or parent.child_by_field_name("name")
                if name_node:
                    return name_node.text.decode("utf8")

        # 处理变量声明中的函数
        if node.type in ["lexical_declaration", "variable_declaration"]:
            for child in node.children:
                if child.type == "variable_declarator":
                    value_node = child.child_by_field_name("value")
                    if value_node and value_node.type == "arrow_function":
                        name_node = child.child_by_field_name("name")
                        if name_node:
                            return name_node.text.decode("utf8")

        return ""
    except Exception:
        return ""


def deduplicate_function_nodes(function_nodes: list[dict]) -> list[dict]:
    """
    去重函数节点，避免重复处理相同位置的节点
    """
    seen_positions = set()
    unique_function_nodes = []

    # 按照起始位置排序
    function_nodes.sort(key=lambda x: x["start_point"])

    for node in function_nodes:
        pos_key = (node["start_point"][0], node["end_point"][0])
        if pos_key not in seen_positions:
            unique_function_nodes.append(node)
            seen_positions.add(pos_key)

    return unique_function_nodes


def create_function_snippets(
    function_nodes: list[dict],
    code: str,
    path: str,
    import_export_info: dict,
    total_lines: int,
) -> list[Snippet]:
    """
    基于函数节点创建snippet列表，确保完整覆盖且无重叠
    """
    snippets = []
    covered_ranges = []
    lines = code.split("\n")

    # 处理文件开头到第一个函数之间的部分
    if function_nodes and function_nodes[0]["start_point"][0] > 0:
        first_node_start = function_nodes[0]["start_point"][0] + 1
        if first_node_start > 1:
            header_snippet = Snippet(
                content=code,
                start=1,
                end=first_node_start - 1,
                file_path=path,
            )
            header_metadata = assign_import_export_to_snippet(
                1, first_node_start - 1, import_export_info, header_snippet
            )
            header_snippet.metadata = header_metadata
            snippets.append(header_snippet)
            covered_ranges.append((1, first_node_start - 1))

    # 处理每个函数节点
    for node in function_nodes:
        start_line = node["start_point"][0] + 1
        end_line = node["end_point"][0] + 1

        # 确保行号在有效范围内
        start_line = max(1, start_line)
        end_line = min(total_lines, end_line)

        # 检查并填补空隙
        if covered_ranges:
            last_end = covered_ranges[-1][1]
            if start_line > last_end + 1:
                gap_start = last_end + 1
                gap_end = start_line - 1

                # 判断是否应该与下一个函数合并
                if should_merge_gap_with_next_function(
                    code, gap_start, gap_end, start_line
                ):
                    start_line = gap_start
                else:
                    # 创建空隙snippet
                    gap_snippet = Snippet(
                        content=code,
                        start=gap_start,
                        end=gap_end,
                        file_path=path,
                    )
                    gap_metadata = assign_import_export_to_snippet(
                        gap_start, gap_end, import_export_info, gap_snippet
                    )
                    gap_snippet.metadata = gap_metadata
                    snippets.append(gap_snippet)
                    covered_ranges.append((gap_start, gap_end))
            elif start_line <= last_end:
                # 有重叠，调整起始行以避免重复
                start_line = last_end + 1
                if start_line > end_line:
                    # 如果调整后起始行超过结束行，跳过这个节点
                    continue

        # 创建函数snippet（确保不与已有范围重叠）
        if not covered_ranges or start_line > covered_ranges[-1][1]:
            snippet = Snippet(
                content=code,
                start=start_line,
                end=end_line,
                file_path=path,
            )
            metadata = assign_import_export_to_snippet(
                start_line, end_line, import_export_info, snippet
            )
            snippet.metadata = metadata
            snippets.append(snippet)
            covered_ranges.append((start_line, end_line))

    # 处理文件末尾的剩余内容
    if covered_ranges and covered_ranges[-1][1] < total_lines:
        tail_start = covered_ranges[-1][1] + 1
        tail_snippet = Snippet(
            content=code,
            start=tail_start,
            end=total_lines,
            file_path=path,
        )
        tail_metadata = assign_import_export_to_snippet(
            tail_start, total_lines, import_export_info, tail_snippet
        )
        tail_snippet.metadata = tail_metadata
        snippets.append(tail_snippet)

        return snippets


def combine_snippets_intelligently(
    snippets: list[Snippet], code: str, path: str
) -> list[Snippet]:
    """
    智能组合snippet，基于40行目标大小和语义完整性
    优化策略：优先组合小块，合理分割大块
    """
    if not snippets:
        return snippets

    # 第一步：处理超长snippet
    processed_snippets = []
    for snippet in snippets:
        lines_count = snippet.end - snippet.start + 1
        if lines_count > MAX_CHUNK_LINES:
            # 分割超长snippet
            sub_snippets = split_long_snippet_intelligently(snippet, code, path)
            processed_snippets.extend(sub_snippets)
        else:
            processed_snippets.append(snippet)

    # 第二步：智能组合策略
    final_snippets = []
    i = 0

    while i < len(processed_snippets):
        current_snippet = processed_snippets[i]
        current_size = current_snippet.end - current_snippet.start + 1

        # 如果当前snippet已经接近目标大小，直接添加
        if current_size >= DEFAULT_TARGET_LINES * 0.8:
            final_snippets.append(current_snippet)
            i += 1
            continue

        # 尝试与后续snippet组合
        group = [current_snippet]
        group_size = current_size
        j = i + 1

        while j < len(processed_snippets):
            next_snippet = processed_snippets[j]
            next_size = next_snippet.end - next_snippet.start + 1

            # 检查是否可以组合
            # 对于小的注释块，放宽合并限制到80行
            max_allowed_size = DEFAULT_TARGET_LINES * 1.3  # 52行

            # 如果当前组的第一个块是小的注释块，允许合并到80行
            first_snippet = group[0]
            first_size = first_snippet.end - first_snippet.start + 1
            if first_size <= 3:  # 小块（通常是注释）
                lines = code.split("\n")
                first_content = "\n".join(
                    lines[first_snippet.start - 1 : first_snippet.end]
                ).strip()
                if first_content.startswith("//") or first_content.startswith("#"):
                    max_allowed_size = MAX_CHUNK_LINES  # 放宽到80行

            if group_size + next_size <= max_allowed_size:
                # 检查代码行是否相邻（避免跨越大的空隙）
                last_end = group[-1].end
                next_start = next_snippet.start

                if next_start <= last_end + 5:  # 允许小的间隙
                    group.append(next_snippet)
                    group_size += next_size
                    j += 1

                    # 如果已经接近理想大小，停止组合
                    if group_size >= DEFAULT_TARGET_LINES * 0.9:
                        break
                else:
                    # 间隙太大，停止组合
                    break
            else:
                # 组合后会太大，停止
                break

        # 创建组合结果
        if len(group) == 1:
            final_snippets.append(group[0])
        else:
            combined = combine_snippet_group(group, code, path)
            final_snippets.append(combined)

        i = j

    # 第三步：应用边界修复
    lines = code.split("\n")

    # 将snippets转换为ranges进行边界修复
    ranges = [(snippet.start, snippet.end) for snippet in final_snippets]
    fixed_ranges = fix_chunk_boundaries(ranges, lines, MAX_CHUNK_LINES)

    # 将修复后的ranges转换回snippets
    if fixed_ranges != ranges:
        final_snippets = []
        for start, end in fixed_ranges:
            snippet = Snippet(
                content=code,
                start=start,
                end=end,
                file_path=path,
            )
            # 尝试保留原有的metadata
            for orig_snippet in snippets:
                if (
                    orig_snippet.start <= start <= orig_snippet.end
                    or orig_snippet.start <= end <= orig_snippet.end
                ):
                    if hasattr(orig_snippet, "metadata"):
                        snippet.metadata = orig_snippet.metadata
                    break
            final_snippets.append(snippet)

    # 过滤掉空的snippet
    return filter_empty_snippets(final_snippets)


def split_long_snippet_intelligently(
    snippet: Snippet, code: str, path: str
) -> list[Snippet]:
    """
    智能分割长snippet，优先按照内联函数边界分割
    """
    # 首先尝试按内联函数分割
    try:
        sub_snippets = split_long_snippet_by_inline_functions(snippet, code, path)
        if sub_snippets and len(sub_snippets) > 1:
            return sub_snippets
    except Exception:
        pass

    # 如果无法按内联函数分割，使用智能分块
    lines = code.split("\n")
    ranges = split_large_block(
        lines, snippet.start, snippet.end, DEFAULT_TARGET_LINES, MAX_CHUNK_LINES
    )

    # 修复分块边界：将开头是结束符号的行移动到上一个块
    ranges = fix_chunk_boundaries(ranges, lines, MAX_CHUNK_LINES)

    sub_snippets = []
    for start, end in ranges:
        sub_snippet = Snippet(
            content=code,
            start=start,
            end=end,
            file_path=path,
        )
        if hasattr(snippet, "metadata"):
            sub_snippet.metadata = snippet.metadata
        sub_snippets.append(sub_snippet)

    return sub_snippets


def combine_snippet_group(snippets: list[Snippet], code: str, path: str) -> Snippet:
    """
    将多个snippet组合成一个snippet
    """
    if not snippets:
        return None

    if len(snippets) == 1:
        return snippets[0]

    # 找到组合范围
    start_line = min(s.start for s in snippets)
    end_line = max(s.end for s in snippets)

    combined = Snippet(
        content=code,
        start=start_line,
        end=end_line,
        file_path=path,
    )

    # 合并metadata
    if hasattr(snippets[0], "metadata"):
        combined_metadata = {"imports": []}
        seen_imports = set()

        for snippet in snippets:
            if hasattr(snippet, "metadata") and snippet.metadata:
                for import_text in snippet.metadata.get("imports", []):
                    if import_text not in seen_imports:
                        combined_metadata["imports"].append(import_text)
                        seen_imports.add(import_text)

        combined.metadata = combined_metadata

    return combined


def chunk_code_for_extension_sources(code: str, path: str) -> list[Snippet]:
    """
    针对extension_sources的专门处理函数
    只保留export的内容，如果文件没有export则忽略
    将整个文件的export内容合并到一个snippet中
    """
    ext = path.split(".")[-1]
    if ext not in extension_to_language:
        # 不支持的文件类型，返回空列表
        return []

    language = extension_to_language[ext]

    # 提取export信息
    import_export_info = extract_import_export_info(path, code)
    exports = import_export_info.get("exports", [])

    # 如果没有export，忽略这个文件
    if not exports:
        return []

    # 检查是否全是重新导出语句（export * from 或 export { ... } from）
    if is_only_reexports(exports):
        return []

    # 提取export相关的内容
    export_content = extract_export_content(code, path, exports)

    if not export_content.strip():
        return []

    # 创建单个snippet包含所有export内容
    lines = code.split("\n")
    snippet = Snippet(
        content=export_content,  # 使用提取的export内容
        start=1,
        end=len(lines),
        file_path=path,
    )

    # 不再保存import信息到metadata
    metadata = {}
    snippet.metadata = metadata

    # 检查内容是否为空
    if snippet.get_snippet(add_ellipsis=False, add_lines=False).strip():
        return [snippet]
    else:
        return []


def is_only_reexports(exports: list) -> bool:
    """
    检查export列表是否全是重新导出语句
    重新导出语句包括：
    - export * from './module'
    - export { something } from './module'
    - export { default } from './module'
    """
    if not exports:
        return False

    for export_item in exports:
        export_text = export_item.get("text", "")

        # 检查是否是重新导出语句
        if " from " not in export_text:
            # 不是重新导出语句，说明有本地定义的export
            return False

        # 进一步检查是否是真正的重新导出
        # export * from './module' 或 export { ... } from './module'
        if not (
            export_text.strip().startswith("export *")
            or (export_text.strip().startswith("export {") and " from " in export_text)
        ):
            # 不是标准的重新导出格式
            return False

    # 所有export都是重新导出语句
    return True


def extract_export_content(code: str, path: str, exports: list) -> str:
    """
    从代码中提取export相关的内容
    只保留export函数/类的接口定义、返回值、public变量、export的常量和枚举值
    删除函数体内部实现，只保留签名
    """
    ext = path.split(".")[-1]
    if ext not in extension_to_language:
        return ""

    language = extension_to_language[ext]

    try:
        parser = get_parser(language)
        tree = parser.parse(bytes(code, "utf8"))

        # 收集所有export相关的节点和被引用的类/函数/常量
        export_nodes = []
        referenced_classes = set()
        referenced_constants = set()
        lines = code.split("\n")

        def traverse_node(node):
            # 检查是否是export语句
            if node.type == "export_statement":
                export_nodes.append(node)
                # 检查export default是否引用了类或函数
                export_text = node.text.decode("utf8")
                if "export default" in export_text:
                    # 提取被引用的标识符
                    if "new " in export_text:
                        # export default new ClassName()
                        import re

                        match = re.search(r"new\s+(\w+)", export_text)
                        if match:
                            referenced_classes.add(match.group(1))
                    else:
                        # export default ClassName 或 export default ConstantName
                        parts = (
                            export_text.replace("export default", "")
                            .strip()
                            .rstrip(";")
                        )
                        if (
                            parts
                            and not parts.startswith("{")
                            and not parts.startswith("(")
                        ):
                            identifier = parts.split("(")[0].strip()
                            referenced_classes.add(identifier)
                            referenced_constants.add(identifier)

            # 递归处理子节点
            for child in node.children:
                traverse_node(child)

        traverse_node(tree.root_node)

        # 收集被引用的类定义和常量定义
        class_nodes = []
        constant_nodes = []

        def find_definitions(node):
            if node.type == "class_declaration":
                # 获取类名
                name_node = node.child_by_field_name("name")
                if name_node:
                    class_name = name_node.text.decode("utf8")
                    if class_name in referenced_classes:
                        class_nodes.append(node)
            elif node.type in ["lexical_declaration", "variable_declaration"]:
                # 检查是否是常量定义 (const/let/var)
                for child in node.children:
                    if child.type == "variable_declarator":
                        name_node = child.child_by_field_name("name")
                        if name_node:
                            const_name = name_node.text.decode("utf8")
                            if const_name in referenced_constants:
                                constant_nodes.append(node)

            for child in node.children:
                find_definitions(child)

        find_definitions(tree.root_node)

        # 提取export内容
        export_content_parts = []

        # 不再添加import语句到内容中

        # 首先处理被引用的常量定义（对象形式）
        for const_node in constant_nodes:
            const_content = extract_constant_interface(
                const_node.text.decode("utf8"), language
            )
            if const_content:
                export_content_parts.append(const_content)
                export_content_parts.append("")  # 添加空行

        # 然后处理被引用的类定义
        for class_node in class_nodes:
            class_content = extract_class_interface(
                class_node.text.decode("utf8"), language
            )
            if class_content:
                export_content_parts.append(class_content)
                export_content_parts.append("")  # 添加空行

        # 最后处理每个export节点
        for node in export_nodes:
            content = extract_export_node_content(node, lines, language)
            if content:
                export_content_parts.append(content)

        return "\n".join(export_content_parts)

    except Exception as e:
        logger.error(f"提取export内容失败: {str(e)}")
        return ""


def extract_export_node_content(node, lines: list, language: str) -> str:
    """
    提取单个export节点的内容，只保留接口定义
    """
    start_line = node.start_point[0]
    end_line = node.end_point[0]

    # 获取export语句的完整文本
    export_text = node.text.decode("utf8")

    # 根据export类型进行不同处理
    if "export default" in export_text:
        # 处理export default
        return process_export_default(node, lines, language)
    elif "export {" in export_text:
        # 处理export { ... }
        return export_text
    elif (
        "export const" in export_text
        or "export let" in export_text
        or "export var" in export_text
    ):
        # 处理export变量/常量
        return export_text
    elif "export function" in export_text:
        # 处理export函数
        return process_export_function(node, lines, language)
    elif "export class" in export_text:
        # 处理export类
        return process_export_class(node, lines, language)
    elif "export interface" in export_text or "export type" in export_text:
        # 处理TypeScript接口和类型
        return export_text
    elif "export enum" in export_text:
        # 处理枚举
        return export_text
    else:
        # 其他export语句
        return export_text


def process_export_default(node, lines: list, language: str) -> str:
    """
    处理export default语句
    """
    export_text = node.text.decode("utf8")

    # 如果是简单的export default语句（如export default MyClass），直接返回
    if export_text.count("\n") <= 2:
        return export_text

    # 如果是复杂的export default，尝试提取接口
    if "function" in export_text:
        return extract_function_signature(export_text, language)
    elif "class" in export_text:
        return extract_class_interface(export_text, language)
    else:
        return export_text


def process_export_function(node, lines: list, language: str) -> str:
    """
    处理export function，只保留函数签名
    """
    export_text = node.text.decode("utf8")
    return extract_function_signature(export_text, language)


def process_export_class(node, lines: list, language: str) -> str:
    """
    处理export class，只保留public接口
    """
    export_text = node.text.decode("utf8")
    return extract_class_interface(export_text, language)


def extract_function_signature(function_text: str, language: str) -> str:
    """
    从函数文本中提取函数签名，移除函数体
    """
    lines = function_text.split("\n")
    signature_lines = []

    brace_count = 0
    in_function_body = False

    for line in lines:
        if not in_function_body:
            signature_lines.append(line)
            # 检查是否进入函数体
            if "{" in line:
                brace_count += line.count("{")
                brace_count -= line.count("}")
                if brace_count > 0:
                    in_function_body = True
                    # 如果这行包含{，只保留到{为止
                    if "{" in line:
                        brace_pos = line.find("{")
                        signature_lines[-1] = (
                            line[: brace_pos + 1] + " /* implementation removed */ }"
                        )
                        break

    return "\n".join(signature_lines)


def extract_class_interface(class_text: str, language: str) -> str:
    """
    从类文本中提取类接口，保留方法签名但用...替代实现
    """
    lines = class_text.split("\n")
    interface_lines = []

    brace_count = 0
    in_class_body = False
    in_method_body = False
    method_brace_count = 0
    current_method_lines = []

    for line in lines:
        stripped_line = line.strip()

        if not in_class_body:
            interface_lines.append(line)
            if "{" in line:
                brace_count += line.count("{")
                brace_count -= line.count("}")
                if brace_count > 0:
                    in_class_body = True
        else:
            # 在类体内
            if stripped_line.startswith("private") and not in_method_body:
                # 跳过private成员（但如果在方法体内则继续处理）
                continue
            elif stripped_line == "}":
                # 检查是否是方法结束或类结束
                if in_method_body:
                    method_brace_count -= stripped_line.count("}")
                    if method_brace_count <= 0:
                        # 方法结束，添加...并结束方法
                        interface_lines.append("    ...")
                        interface_lines.append("  };")
                        in_method_body = False
                        method_brace_count = 0
                        current_method_lines = []
                else:
                    # 类结束
                    brace_count -= 1
                    if brace_count == 0:
                        interface_lines.append(line)
                        break
            elif in_method_body:
                # 在方法体内，计算大括号数量
                method_brace_count += line.count("{")
                method_brace_count -= line.count("}")
                if method_brace_count <= 0:
                    # 方法结束
                    interface_lines.append("    ...")
                    interface_lines.append("  };")
                    in_method_body = False
                    method_brace_count = 0
                    current_method_lines = []
            elif stripped_line:
                # 处理类成员
                if ("=" in line and "=>" in line) or (
                    stripped_line.endswith("{") and "(" in line
                ):
                    # 这是一个方法定义（箭头函数或普通方法）
                    if "=>" in line:
                        # 箭头函数方法
                        arrow_pos = line.find("=>")
                        if "{" in line[arrow_pos:]:
                            # 有方法体的箭头函数
                            method_signature = line[: arrow_pos + 2].strip()
                            interface_lines.append(f"  {method_signature} {{")
                            in_method_body = True
                            method_brace_count = line[arrow_pos:].count("{") - line[
                                arrow_pos:
                            ].count("}")
                        else:
                            # 单行箭头函数，替换为...
                            method_signature = line[: arrow_pos + 2].strip()
                            interface_lines.append(f"  {method_signature} {{ ... }};")
                    elif "{" in line and "(" in line:
                        # 普通方法
                        brace_pos = line.find("{")
                        method_signature = line[:brace_pos].strip()
                        interface_lines.append(f"  {method_signature} {{")
                        in_method_body = True
                        method_brace_count = line[brace_pos:].count("{") - line[
                            brace_pos:
                        ].count("}")
                elif (
                    "(" in stripped_line
                    and ")" in stripped_line
                    and (
                        stripped_line.startswith("public")
                        or stripped_line.startswith("static")
                        or stripped_line.startswith("constructor")
                        or not stripped_line.startswith("private")
                    )
                ):
                    # 方法声明（没有实现体）
                    if "{" in line:
                        # 有实现的方法
                        brace_pos = line.find("{")
                        method_signature = line[:brace_pos].strip()
                        interface_lines.append(f"  {method_signature} {{")
                        in_method_body = True
                        method_brace_count = line[brace_pos:].count("{") - line[
                            brace_pos:
                        ].count("}")
                    else:
                        # 方法声明
                        interface_lines.append(line)
                elif not stripped_line.startswith(
                    "//"
                ) and not stripped_line.startswith("/*"):
                    # 其他有效行（如属性声明），但跳过注释
                    interface_lines.append(line)

    return "\n".join(interface_lines)


def extract_constant_interface(const_text: str, language: str) -> str:
    """
    从常量定义中提取接口，特别处理对象形式的常量
    """
    lines = const_text.split("\n")
    interface_lines = []

    brace_count = 0
    in_object_body = False
    in_method_body = False
    method_brace_count = 0

    for line in lines:
        stripped_line = line.strip()

        if not in_object_body:
            interface_lines.append(line)
            if "{" in line:
                brace_count += line.count("{")
                brace_count -= line.count("}")
                if brace_count > 0:
                    in_object_body = True
        else:
            # 在对象体内
            if stripped_line == "}":
                # 检查是否是方法结束或对象结束
                if in_method_body:
                    method_brace_count -= stripped_line.count("}")
                    if method_brace_count <= 0:
                        # 方法结束，添加...并结束方法
                        interface_lines.append("    ...")
                        interface_lines.append("  },")
                        in_method_body = False
                        method_brace_count = 0
                else:
                    # 对象结束
                    brace_count -= 1
                    if brace_count == 0:
                        interface_lines.append(line)
                        break
            elif in_method_body:
                # 在方法体内，计算大括号数量
                method_brace_count += line.count("{")
                method_brace_count -= line.count("}")
                if method_brace_count <= 0:
                    # 方法结束
                    interface_lines.append("    ...")
                    interface_lines.append("  },")
                    in_method_body = False
                    method_brace_count = 0
            elif stripped_line:
                # 处理对象成员
                if ":" in line and ("=>" in line or "{" in line):
                    # 这是一个方法定义
                    if "=>" in line:
                        # 箭头函数方法
                        arrow_pos = line.find("=>")
                        if "{" in line[arrow_pos:]:
                            # 有方法体的箭头函数
                            method_signature = line[: arrow_pos + 2].strip()
                            interface_lines.append(f"  {method_signature} {{")
                            in_method_body = True
                            method_brace_count = line[arrow_pos:].count("{") - line[
                                arrow_pos:
                            ].count("}")
                        else:
                            # 单行箭头函数，替换为...
                            method_signature = line[: arrow_pos + 2].strip()
                            interface_lines.append(f"  {method_signature} {{ ... }},")
                    elif "{" in line:
                        # 普通方法
                        brace_pos = line.find("{")
                        method_signature = line[:brace_pos].strip()
                        interface_lines.append(f"  {method_signature} {{")
                        in_method_body = True
                        method_brace_count = line[brace_pos:].count("{") - line[
                            brace_pos:
                        ].count("}")
                elif ":" in stripped_line and not stripped_line.startswith("//"):
                    # 其他属性定义
                    interface_lines.append(line)
                elif not stripped_line.startswith(
                    "//"
                ) and not stripped_line.startswith("/*"):
                    # 其他有效行，但跳过注释
                    interface_lines.append(line)

    return "\n".join(interface_lines)


def get_function_name(file_name: str, source_code: str, line_number: int):
    ext = file_name.split(".")[-1]
    if ext in extension_to_language:
        language = extension_to_language[ext]
    else:
        return None
    type_mapping_per_language = {
        "python": "function_definition",
        "tsx": "function_declaration",
        "js": "function_declaration",
    }
    function_type_string = type_mapping_per_language.get(language)
    parser = get_parser(language)

    # Parse the source code
    tree = parser.parse(bytes(source_code, "utf8"))

    # Get the root node of the syntax tree
    root_node = tree.root_node

    # Find the function node that contains the given line number
    function_node = root_node.descendant_for_point_range(
        (line_number, 0), (line_number, 1)
    )

    max_depth = 25  # Maximum depth to search for the function node
    while function_node.type != function_type_string:
        function_node = function_node.parent
        max_depth -= 1
        if max_depth == 0 or function_node is None:
            return None

    # Extract the function name
    function_name = function_node.child_by_field_name("name").text.decode("utf8")

    return function_name


def extract_import_export_info(file_name, code):
    """
    提取文件中的import和export信息
    返回包含import和export语句的字典
    """
    ext = file_name.split(".")[-1]
    if ext in extension_to_language:
        language = extension_to_language[ext]
    else:
        return {"imports": [], "exports": []}

    # 定义不同语言的import/export节点类型
    import_export_mapping = {
        "typescript": ["import_statement", "export_statement"],
        "tsx": ["import_statement", "export_statement"],
        "javascript": ["import_statement", "export_statement"],
        "python": ["import_statement", "import_from_statement"],
    }

    import_export_types = import_export_mapping.get(language, [])
    if not import_export_types:
        return {"imports": [], "exports": []}

    parser = get_parser(language)
    tree = parser.parse(bytes(code, "utf8"))

    imports = []
    exports = []

    def traverse_node(node):
        if node.type in import_export_types:
            line_start = node.start_point[0] + 1
            line_end = node.end_point[0] + 1
            text = node.text.decode("utf8")

            if "import" in node.type:
                imports.append(
                    {
                        "type": node.type,
                        "text": text,
                        "start_line": line_start,
                        "end_line": line_end,
                    }
                )
            elif "export" in node.type:
                exports.append(
                    {
                        "type": node.type,
                        "text": text,
                        "start_line": line_start,
                        "end_line": line_end,
                    }
                )

        # 递归处理子节点
        for child in node.children:
            traverse_node(child)

    traverse_node(tree.root_node)

    return {"imports": imports, "exports": exports}


def assign_import_export_to_snippet(
    snippet_start, snippet_end, import_export_info, snippet_obj=None
):
    """
    为代码片段分配相关的import信息
    返回包含相关import的metadata字典

    参数:
        snippet_start: 代码片段起始行
        snippet_end: 代码片段结束行
        import_export_info: 包含imports和exports的字典
        snippet_obj: Snippet对象，用于获取实际内容检查是否已包含import
    """
    metadata = {"imports": []}

    # 获取snippet的实际内容（如果提供了snippet对象）
    snippet_content = ""
    if snippet_obj:
        snippet_content = snippet_obj.get_snippet(add_ellipsis=False, add_lines=False)

    # 分配import信息 - 只添加content中不存在的import
    for item in import_export_info["imports"]:
        import_text = item["text"]
        # 检查content中是否已经包含这个import语句
        if snippet_content and import_text in snippet_content:
            # 如果content中已经包含，跳过不添加到metadata
            continue
        else:
            # 如果content中不包含，添加到metadata以提供完整的依赖信息
            metadata["imports"].append(import_text)

    return metadata


def merge_metadata(metadata1, metadata2):
    """
    合并两个metadata字典，去重并保持顺序
    """
    merged = {"imports": []}

    # 合并imports，去重
    seen_imports = set()
    for metadata in [metadata1, metadata2]:
        for import_text in metadata.get("imports", []):
            if import_text not in seen_imports:
                merged["imports"].append(import_text)
                seen_imports.add(import_text)

    return merged


def get_function_nodes(file_name, code):
    """
    获取文件中所有函数、方法和类定义的节点
    返回节点列表，每个节点包含类型、名称、起始行和结束行
    """
    ext = file_name.split(".")[-1]
    if ext in extension_to_language:
        language = extension_to_language[ext]
    else:
        return []

    # 定义不同语言中的函数、方法和类定义节点类型
    type_mapping_per_language = {
        "typescript": [
            "function_declaration",
            "class_declaration",
            "interface_declaration",  # TypeScript接口定义
            "type_alias_declaration",  # TypeScript类型别名
            "method_definition",
            "arrow_function",
            "export_statement",
            "lexical_declaration",  # const/let 声明的箭头函数
            "variable_declaration",  # var 声明的箭头函数
        ],
        "tsx": [
            "function_declaration",
            "class_declaration",
            "interface_declaration",  # TypeScript接口定义
            "type_alias_declaration",  # TypeScript类型别名
            "method_definition",
            "arrow_function",
            "export_statement",
            "lexical_declaration",  # const/let 声明的箭头函数
            "variable_declaration",  # var 声明的箭头函数
        ],
        "javascript": [
            "function_declaration",
            "class_declaration",
            "method_definition",
            "arrow_function",
            "export_statement",
            "lexical_declaration",
            "variable_declaration",
        ],
    }

    function_type_strings = type_mapping_per_language.get(language)
    if not function_type_strings:
        return []

    parser = get_parser(language)
    tree = parser.parse(bytes(code, "utf8"))

    function_nodes = []

    def traverse_node(node, parent=None):
        # 检查是否是函数、方法或类定义
        if node.type in function_type_strings:
            node_info = {
                "type": node.type,
                "start_point": node.start_point,
                "end_point": node.end_point,
                "start_byte": node.start_byte,
                "end_byte": node.end_byte,
                "parent": parent,
            }

            # 提取名称（如果有）
            if node.type == "class_declaration":
                name_node = node.child_by_field_name("name")
                if name_node:
                    node_info["name"] = name_node.text.decode("utf8")
            elif node.type == "function_declaration":
                name_node = node.child_by_field_name("name")
                if name_node:
                    node_info["name"] = name_node.text.decode("utf8")
            elif node.type == "interface_declaration":
                name_node = node.child_by_field_name("name")
                if name_node:
                    node_info["name"] = name_node.text.decode("utf8")
            elif node.type == "type_alias_declaration":
                name_node = node.child_by_field_name("name")
                if name_node:
                    node_info["name"] = name_node.text.decode("utf8")
            elif node.type == "method_definition":
                name_node = node.child_by_field_name("name")
                if name_node:
                    node_info["name"] = name_node.text.decode("utf8")
            elif node.type == "arrow_function":
                # 箭头函数可能没有名称，尝试从父节点获取
                if parent and parent.type in [
                    "assignment_expression",
                    "pair",
                    "variable_declarator",
                ]:
                    name_node = parent.child_by_field_name(
                        "left"
                    ) or parent.child_by_field_name("name")
                    if name_node:
                        node_info["name"] = name_node.text.decode("utf8")
            elif node.type in ["lexical_declaration", "variable_declaration"]:
                # 检查是否包含箭头函数
                for child in node.children:
                    if child.type == "variable_declarator":
                        value_node = child.child_by_field_name("value")
                        if value_node and value_node.type == "arrow_function":
                            name_node = child.child_by_field_name("name")
                            if name_node:
                                node_info["name"] = name_node.text.decode("utf8")
                                node_info["start_point"] = node.start_point
                                node_info["end_point"] = node.end_point
                                node_info["start_byte"] = node.start_byte
                                node_info["end_byte"] = node.end_byte
                                node_info["type"] = "arrow_function_declaration"

            function_nodes.append(node_info)

        # 递归处理子节点
        for child in node.children:
            traverse_node(child, node)

    traverse_node(tree.root_node)
    return function_nodes


def is_comment_line(line: str) -> bool:
    """
    识别是否为注释行，支持多种注释格式
    - // 单行注释 (JavaScript, TypeScript, C++等)
    - /* */ 多行注释开始或内容
    - /** */ 文档注释
    - # 单行注释 (Python等)
    - * 多行注释内容行
    """
    stripped = line.strip()
    if not stripped:
        return False

    return (
        stripped.startswith("//")  # 单行注释
        or stripped.startswith("/*")  # 多行注释开始
        or stripped.startswith("/**")  # 文档注释开始
        or stripped.startswith("*")  # 多行注释内容行
        or stripped.endswith("*/")  # 多行注释结束
        or stripped.startswith("#")  # Python注释
    )


def is_multiline_comment_block(lines: list[str], start_idx: int, end_idx: int) -> bool:
    """
    检查指定范围是否包含多行注释块
    """
    for i in range(start_idx, min(end_idx + 1, len(lines))):
        line = lines[i].strip()
        if line.startswith("/**") or line.startswith("/*"):
            # 找到多行注释开始，检查是否有结束
            for j in range(i, min(end_idx + 1, len(lines))):
                if "*/" in lines[j]:
                    return True
        return True  # 开始了但没结束，也算多行注释块
    return False


def find_comment_block_end(lines: list[str], start_idx: int) -> int:
    """
    从给定位置开始，找到注释块的结束位置
    返回注释块最后一行的索引
    """
    end_idx = start_idx

    # 如果是多行注释
    if lines[start_idx].strip().startswith("/*") or lines[start_idx].strip().startswith(
        "/**"
    ):
        for i in range(start_idx, len(lines)):
            end_idx = i
            if "*/" in lines[i]:
                break
        return end_idx

    # 如果是连续的单行注释
    for i in range(start_idx, len(lines)):
        line = lines[i].strip()
        if is_comment_line(lines[i]) or not line:  # 注释行或空行
            end_idx = i
        else:
            break

    return end_idx


def get_next_non_comment_line(lines: list[str], start_idx: int) -> int:
    """
    从指定位置开始，找到下一个非注释、非空行的位置
    """
    for i in range(start_idx, len(lines)):
        line = lines[i].strip()
        if line and not is_comment_line(lines[i]):
            return i
    return len(lines)


def is_comment_related_to_next_code(
    lines: list[str], comment_start: int, comment_end: int, next_code_start: int
) -> bool:
    """
    判断注释是否与下一段代码相关
    基于注释内容和位置的启发式判断
    """
    # 检查注释和下一段代码之间的距离
    gap_size = next_code_start - comment_end
    if gap_size > 3:  # 距离太远，可能不相关
        return False

    # 获取注释内容进行简单的语义分析
    comment_text = ""
    for i in range(comment_start, min(comment_end + 1, len(lines))):
        line = lines[i].strip()
        if is_comment_line(lines[i]):
            # 提取注释内容
            if line.startswith("//"):
                comment_text += line[2:].strip() + " "
            elif line.startswith("/*") or line.startswith("/**"):
                content = line[3:] if line.startswith("/**") else line[2:]
                comment_text += content.replace("*/", "").strip() + " "
            elif line.startswith("*"):
                comment_text += line[1:].replace("*/", "").strip() + " "
            elif line.startswith("#"):
                comment_text += line[1:].strip() + " "

    # 获取下一段代码的第一行
    if next_code_start < len(lines):
        next_line = lines[next_code_start].strip()

        # 如果下一行是函数、类、方法定义，注释很可能是相关的
        function_keywords = [
            "function",
            "def",
            "class",
            "const",
            "let",
            "var",
            "export",
            "interface",
            "type",
        ]
        if any(next_line.startswith(keyword) for keyword in function_keywords):
            return True

        # 如果注释包含函数、方法、类等关键词，也可能相关
        code_keywords = [
            "function",
            "method",
            "class",
            "constructor",
            "param",
            "@param",
            "return",
            "@return",
            "throws",
        ]
        if any(keyword in comment_text.lower() for keyword in code_keywords):
            return True

    # 默认情况下，相邻的注释认为是相关的
    return gap_size <= 1


def chunk_code_by_function(code: str, path: str) -> list[Snippet]:
    """
    基于AST进行函数级别的代码拆分
    针对TypeScript和TSX文件，按照函数、方法和类定义进行拆分
    确保所有代码都被包含，不丢失任何内容
    """
    function_nodes = get_function_nodes(path, code)
    lines = code.split("\n")
    total_lines = len(lines)

    # 如果没有找到函数节点，返回整个文件作为一个snippet
    if not function_nodes:
        snippet = Snippet(
            content=code,
            start=1,
            end=total_lines,
            file_path=path,
        )
        # 检查内容是否为空
        if snippet.get_snippet(add_ellipsis=False, add_lines=False).strip():
            return [snippet]
        else:
            return []

    # 提取文件的import和export信息
    import_export_info = extract_import_export_info(path, code)

    # 按照起始位置排序
    function_nodes.sort(key=lambda x: x["start_point"])

    # 去重函数节点，避免重复处理相同位置的节点
    seen_positions = set()
    unique_function_nodes = []
    for node in function_nodes:
        pos_key = (node["start_point"][0], node["end_point"][0])
        if pos_key not in seen_positions:
            unique_function_nodes.append(node)
            seen_positions.add(pos_key)

    function_nodes = unique_function_nodes

    # 创建覆盖范围列表，确保所有代码都被包含
    covered_ranges = []
    snippets = []

    # 处理文件开头到第一个函数之间的部分
    if function_nodes and function_nodes[0]["start_point"][0] > 0:
        first_node_start = function_nodes[0]["start_point"][0] + 1
        if first_node_start > 1:  # 有内容在第一个函数之前
            header_snippet = Snippet(
                content=code,
                start=1,
                end=first_node_start - 1,
                file_path=path,
            )
            header_metadata = assign_import_export_to_snippet(
                1, first_node_start - 1, import_export_info, header_snippet
            )
            header_snippet.metadata = header_metadata
            snippets.append(header_snippet)
            covered_ranges.append((1, first_node_start - 1))

    # 处理每个函数节点
    for node in function_nodes:
        start_line = node["start_point"][0] + 1  # 转换为1-based索引
        end_line = node["end_point"][0] + 1

        # 确保起始行和结束行有效
        start_line = max(1, start_line)
        end_line = min(total_lines, end_line)

        # 检查是否需要填补与前一个节点之间的空隙
        if covered_ranges:
            last_end = covered_ranges[-1][1]
            if start_line > last_end + 1:
                gap_start = last_end + 1
                gap_end = start_line - 1

                # 检查空隙内容是否应该与下一个函数合并
                should_merge_with_next = should_merge_gap_with_next_function(
                    code, gap_start, gap_end, start_line
                )

                if should_merge_with_next:
                    # 将空隙内容与下一个函数合并，调整函数的起始行
                    start_line = gap_start
                    # 注意：不要将gap添加到covered_ranges，因为它将被包含在函数snippet中
                else:
                    # 有空隙，创建一个snippet来填补
                    gap_snippet = Snippet(
                        content=code,
                        start=gap_start,
                        end=gap_end,
                        file_path=path,
                    )
                    gap_metadata = assign_import_export_to_snippet(
                        gap_start, gap_end, import_export_info, gap_snippet
                    )
                    gap_snippet.metadata = gap_metadata
                    snippets.append(gap_snippet)
                    covered_ranges.append((gap_start, gap_end))

        # 创建函数snippet
        snippet = Snippet(
            content=code,
            start=start_line,
            end=end_line,
            file_path=path,
        )
        metadata = assign_import_export_to_snippet(
            start_line, end_line, import_export_info, snippet
        )
        snippet.metadata = metadata
        snippets.append(snippet)
        covered_ranges.append((start_line, end_line))

    # 处理文件末尾的剩余内容
    if covered_ranges:
        last_end = covered_ranges[-1][1]
        if last_end < total_lines:
            # 有剩余内容，创建snippet来包含
            tail_snippet = Snippet(
                content=code,
                start=last_end + 1,
                end=total_lines,
                file_path=path,
            )
            tail_metadata = assign_import_export_to_snippet(
                last_end + 1, total_lines, import_export_info, tail_snippet
            )
            tail_snippet.metadata = tail_metadata
            snippets.append(tail_snippet)
    # 合并重叠的Snippet并按起始行排序
    snippets.sort(key=lambda x: x.start)
    merged_snippets = []

    for snippet in snippets:
        if not merged_snippets:
            merged_snippets.append(snippet)
            continue

        # 检查是否可以与最后一个已合并的snippet合并
        last_snippet = merged_snippets[-1]
        if snippet.start <= last_snippet.end + 1:  # 允许相邻行也合并
            # 合并重叠的snippet
            merged = last_snippet | snippet
            # 重新为合并后的snippet分配metadata，应用去重逻辑
            merged_metadata = assign_import_export_to_snippet(
                merged.start, merged.end, import_export_info, merged
            )
            merged.metadata = merged_metadata
            merged_snippets[-1] = merged
        else:
            merged_snippets.append(snippet)

    # 过滤掉空的snippet
    return filter_empty_snippets(merged_snippets)


def split_long_snippet_by_inline_functions(
    snippet: Snippet, code: str, path: str
) -> list[Snippet]:
    """
    根据内联函数将长snippet分割成多个较小的snippet
    """
    # 获取这个snippet范围内的所有函数节点
    function_nodes = get_function_nodes(path, code)

    # 去重并过滤函数节点
    seen_positions = set()
    unique_nodes = []
    for node in function_nodes:
        pos_key = (node["start_point"][0], node["end_point"][0])
        if pos_key not in seen_positions:
            unique_nodes.append(node)
            seen_positions.add(pos_key)

    # 找到snippet范围内的有意义的内联函数
    inline_functions = []
    for node in unique_nodes:
        node_start = node["start_point"][0] + 1
        node_end = node["end_point"][0] + 1
        line_count = node_end - node_start + 1

        # 如果这个函数在snippet内部，且不是snippet本身，且足够大，但不是主函数
        snippet_lines = snippet.end - snippet.start + 1
        function_coverage = line_count / snippet_lines
        if (
            node_start > snippet.start
            and node_end < snippet.end
            and not (node_start == snippet.start and node_end == snippet.end)
            and line_count >= 15  # 至少15行的内联函数
            and function_coverage
            < 0.8  # 函数不能占snippet的80%以上（避免主函数被当作内联函数）
            and node.get("name") is not None
        ):  # 有名字的函数更有意义
            inline_functions.append(node)

    # 如果没有找到合适的内联函数，尝试找一些较大的匿名函数
    if not inline_functions:
        for node in unique_nodes:
            node_start = node["start_point"][0] + 1
            node_end = node["end_point"][0] + 1
            line_count = node_end - node_start + 1

            if (
                node_start > snippet.start
                and node_end < snippet.end
                and not (node_start == snippet.start and node_end == snippet.end)
                and line_count >= 25
            ):  # 至少25行的大函数
                inline_functions.append(node)

    # 如果还是没有找到合适的内联函数，返回空列表
    if not inline_functions:
        return []

    # 按起始位置排序
    inline_functions.sort(key=lambda x: x["start_point"][0])

    snippets = []
    current_start = snippet.start
    min_chunk_size = 25  # 最小分段大小

    for inline_func in inline_functions:
        inline_start = inline_func["start_point"][0] + 1
        inline_end = inline_func["end_point"][0] + 1

        # 如果内联函数前面有足够的代码，创建一个snippet
        if inline_start - current_start >= min_chunk_size:
            # 检查是否有注释应该与内联函数分组
            lines = code.split("\n")
            adjusted_split_point = inline_start - 1
            comment_start_line = None

            # 检查内联函数前面几行是否有注释应该与函数分组
            for check_line in range(max(current_start, inline_start - 5), inline_start):
                line_content = (
                    lines[check_line - 1].strip() if check_line <= len(lines) else ""
                )
                if (
                    line_content.startswith("//")
                    or line_content.startswith("/*")
                    or line_content.startswith("*")
                ):
                    # 检查这个注释是否应该与下一个函数分组
                    if should_merge_gap_with_next_function(
                        code, check_line, inline_start - 1, inline_start
                    ):
                        comment_start_line = check_line
                        adjusted_split_point = check_line - 1
                        break

            before_lines = adjusted_split_point - current_start + 1
            if before_lines >= min_chunk_size:
                # 如果前面的代码块也很长，进一步分割
                if before_lines > MAX_CHUNK_LINES:
                    # 尝试智能分割，避免在语法结构中间截断
                    sub_snippets = smart_split_code_block(
                        code,
                        current_start,
                        adjusted_split_point,
                        path,
                        snippet.metadata,
                    )
                    snippets.extend(sub_snippets)
                else:
                    # 前面的代码不长，直接创建一个代码块
                    sub_snippet = Snippet(
                        content=code,
                        start=current_start,
                        end=adjusted_split_point,
                        file_path=path,
                    )
                    sub_snippet.metadata = snippet.metadata  # 复用原有的metadata
                    snippets.append(sub_snippet)

                # 如果有注释需要与函数分组，调整current_start以包含注释
                if comment_start_line is not None:
                    current_start = comment_start_line
                else:
                    current_start = adjusted_split_point + 1
            else:
                # 如果调整后的代码块太小，不分割，让注释与函数一起处理
                pass

        # 为内联函数创建snippet（使用可能调整过的current_start）
        function_start = current_start
        function_end = inline_end
        function_lines = function_end - function_start + 1

        if function_lines > MAX_CHUNK_LINES:  # 如果内联函数也很长，使用智能分割
            # 使用智能分割，避免在语法结构中间截断
            sub_snippets = smart_split_code_block(
                code, function_start, function_end, path, snippet.metadata
            )
            snippets.extend(sub_snippets)
        else:
            # 内联函数不长，直接创建一个代码块
            sub_snippet = Snippet(
                content=code,
                start=function_start,
                end=function_end,
                file_path=path,
            )
            sub_snippet.metadata = snippet.metadata  # 复用原有的metadata
            snippets.append(sub_snippet)
        current_start = inline_end + 1

    # 处理剩余的代码
    if (
        current_start <= snippet.end
        and snippet.end - current_start + 1 >= min_chunk_size
    ):
        remaining_lines = snippet.end - current_start + 1
        # 如果剩余代码仍然很长，进一步分割
        if (
            remaining_lines > MAX_CHUNK_LINES
        ):  # 如果剩余部分超过最大分块大小，使用智能分割
            # 使用智能分割，避免在语法结构中间截断
            sub_snippets = smart_split_code_block(
                code, current_start, snippet.end, path, snippet.metadata
            )
            snippets.extend(sub_snippets)
        else:
            # 剩余代码不长，直接创建一个代码块
            sub_snippet = Snippet(
                content=code,
                start=current_start,
                end=snippet.end,
                file_path=path,
            )
            sub_snippet.metadata = snippet.metadata  # 复用原有的metadata
            snippets.append(sub_snippet)

    return snippets


def smart_split_code_block(
    code: str, start_line: int, end_line: int, path: str, metadata: dict
) -> list[Snippet]:
    """
    智能分割代码块，尝试在合适的语法边界进行分割，避免在语句中间截断
    """
    lines = code.split("\n")
    snippets = []
    current_start = start_line
    max_chunk_size = DEFAULT_TARGET_LINES

    while current_start <= end_line:
        # 计算理想的结束位置
        ideal_end = min(current_start + max_chunk_size - 1, end_line)

        # 如果剩余行数不多，直接创建最后一个块
        if end_line - current_start + 1 <= max_chunk_size + 10:
            snippet = Snippet(
                content=code,
                start=current_start,
                end=end_line,
                file_path=path,
            )
            snippet.metadata = metadata
            snippets.append(snippet)
            break

        # 寻找合适的分割点
        actual_end = find_good_split_point(lines, current_start, ideal_end, end_line)

        snippet = Snippet(
            content=code,
            start=current_start,
            end=actual_end,
            file_path=path,
        )
        snippet.metadata = metadata
        snippets.append(snippet)

        current_start = actual_end + 1

    return snippets


def should_avoid_splitting_before_function(
    lines: list, comment_line_num: int, max_end: int
) -> bool:
    """
    检查注释行后面是否紧跟着函数定义，如果是则应该避免在注释处分割
    """
    # 检查注释后面几行是否有函数定义
    for i in range(
        comment_line_num, min(comment_line_num + 5, max_end + 1, len(lines) + 1)
    ):
        if i >= len(lines):
            break
        line = lines[i - 1].strip()  # 转换为0-based索引

        # 跳过空行和其他注释
        if (
            not line
            or line.startswith("//")
            or line.startswith("/*")
            or line.startswith("*")
        ):
            continue

        # 检查是否是函数定义
        if (
            (line.startswith("const ") and "=>" in line)
            or (line.startswith("function "))
            or (line.startswith("export ") and ("function" in line or "=>" in line))
            or ("=" in line and "=>" in line)
        ):
            return True

        # 如果遇到其他代码，停止检查
        break

    return False


def find_good_split_point(
    lines: list, start_line: int, ideal_end: int, max_end: int
) -> int:
    """
    在理想结束位置附近寻找合适的分割点
    增强版：更好地处理注释，确保注释与相关代码保持在一起
    优先级：
    1. 函数/类定义的结束
    2. React组件return语句前的空行
    3. 空行 - 但要检查后面是否有相关注释
    4. 语句的结束（分号、右大括号）
    5. 如果找不到合适的点，使用理想位置
    """
    # 搜索范围：理想位置前后10行，确保能找到合适的分块点
    search_start = max(start_line, ideal_end - 10)
    search_end = min(max_end, ideal_end + 10)

    best_split = ideal_end
    best_priority = 0

    # 检查是否有return语句在搜索范围内，如果有则优先在return语句前分块
    return_line = None
    for line_num in range(search_start, search_end + 1):
        if line_num >= len(lines):
            break
        line = lines[line_num - 1].strip()
        if line.startswith("return (") or line == "return (":
            return_line = line_num
            break

    for line_num in range(search_start, search_end + 1):
        if line_num >= len(lines):
            break

        line = lines[line_num - 1].strip()  # 转换为0-based索引
        priority = 0

        # 优先级1：函数/类定义的结束（右大括号单独一行）
        if line == "}" or line == "})" or line == "},":
            priority = 4
        # 优先级2：React组件return语句前的空行（特殊处理）
        elif line == "" and return_line and line_num == return_line - 1:
            priority = 5  # 最高优先级
        # 优先级3：空行 - 但要检查后面是否有相关注释
        elif line == "":
            # 检查空行后面是否紧跟注释，如果是则降低优先级
            next_line_idx = line_num
            if next_line_idx < len(lines):
                next_line = lines[next_line_idx].strip()
                if is_comment_line(lines[next_line_idx]):
                    # 检查注释后面是否有代码，如果有则避免在空行处分割
                    code_after_comment = get_next_non_comment_line(lines, next_line_idx)
                    if code_after_comment < len(lines):
                        priority = 0  # 避免分割
                    else:
                        priority = 3
                else:
                    priority = 3
            else:
                priority = 3
        # 优先级4：语句结束（但排除单独的左大括号，特别是JSX表达式开始）
        elif line.endswith(";") or line.endswith(","):
            priority = 2
        # 优先级5：以左大括号结尾但不是单独的大括号（如函数定义行）
        elif line.endswith("{") and line.strip() != "{":
            priority = 2
        # 优先级6：注释行 - 但要检查是否应该与下一个函数分组
        elif line.startswith("//") or line.startswith("/*") or line.startswith("*"):
            # 检查注释后面是否紧跟着函数定义，如果是则避免在此处分割
            if should_avoid_splitting_before_function(lines, line_num, max_end):
                priority = 0  # 避免在此处分块
            else:
                priority = 1
        # 特别处理：避免在单独的左大括号处分块（通常是JSX表达式开始）
        elif line.strip() == "{":
            priority = 0  # 最低优先级，避免在此处分块
        # 特别处理：避免在return语句内部分块
        elif return_line and line_num >= return_line:
            priority = 0  # 避免在return语句内部分块

        # 选择优先级最高的，如果优先级相同则选择更接近理想位置的
        if priority > best_priority or (
            priority == best_priority
            and abs(line_num - ideal_end) < abs(best_split - ideal_end)
        ):
            best_split = line_num
            best_priority = priority

    return best_split


def extract_definitions(file_name, code):
    """
    提取文件中的函数、方法和类定义
    用于调试和测试
    """
    ext = file_name.split(".")[-1]
    if ext in extension_to_language:
        language = extension_to_language[ext]
    else:
        return None

    type_mapping_per_language = {
        "typescript": ["class_declaration", "method_definition"],
        "tsx": [
            "function_declaration",
            "class_declaration",
            "method_definition",
            "arrow_function",
            "export_statement",
        ],
        "javascript": [
            "function_declaration",
            "class_declaration",
            "method_definition",
        ],
    }
    function_type_strings = type_mapping_per_language.get(language)

    parser = get_parser(language)
    tree = parser.parse(bytes(code, "utf8"))

    def traverse_node(node):
        if node.type in function_type_strings:
            if node.type == "class_declaration":
                class_name = node.child_by_field_name("name").text.decode("utf8")
            elif node.type == "function_declaration":
                function_name = node.child_by_field_name("name").text.decode("utf8")
            elif node.type == "method_definition":
                method_name = node.child_by_field_name("name").text.decode("utf8")

        for child in node.children:
            traverse_node(child)

    traverse_node(tree.root_node)


def extract_jsx_boundaries(tree, source_code_str: str) -> list[tuple[int, int, str]]:
    """
    专门提取JSX元素的边界，确保JSX标签的完整性
    返回 (start_line, end_line, jsx_type) 的列表
    """
    jsx_boundaries = []
    lines = source_code_str.splitlines()

    def traverse_jsx(node: Node):
        if node.type in ["jsx_element", "jsx_fragment", "jsx_self_closing_element"]:
            start_line = node.start_point[0] + 1
            end_line = node.end_point[0] + 1

            jsx_boundaries.append((start_line, end_line, node.type))

        for child in node.children:
            traverse_jsx(child)

    traverse_jsx(tree.root_node)
    return jsx_boundaries


def extract_python_boundaries(tree, source_code_str: str) -> list[tuple[int, int, str]]:
    """
    专门提取Python语法结构的边界，确保Python语法的完整性
    返回 (start_line, end_line, python_type) 的列表
    """
    python_boundaries = []

    def traverse_python(node: Node):
        # Python特有的语法结构
        if node.type in [
            "function_definition",
            "class_definition",
            "async_function_definition",
            "decorated_definition",
            "with_statement",
            "try_statement",
            "if_statement",
            "for_statement",
            "while_statement",
            "match_statement",
            "list_comprehension",
            "dictionary_comprehension",
            "set_comprehension",
            "generator_expression",
            "lambda",
        ]:
            start_line = node.start_point[0] + 1
            end_line = node.end_point[0] + 1

            python_boundaries.append((start_line, end_line, node.type))

        for child in node.children:
            traverse_python(child)

    traverse_python(tree.root_node)
    return python_boundaries


def is_python_boundary_safe(lines: list[str], start_line: int, end_line: int) -> bool:
    """
    检查在指定位置分割是否会破坏Python语法结构的完整性
    """
    if end_line < 1 or end_line >= len(lines):
        return True

    end_line_content = lines[end_line - 1].strip()

    # Python特有的不安全分割模式
    python_incomplete_patterns = [
        r"^\s*def\s+\w+\s*\([^)]*$",  # 函数定义未完成
        r"^\s*class\s+\w+\s*\([^)]*$",  # 类定义未完成
        r"^\s*async\s+def\s+\w+\s*\([^)]*$",  # 异步函数定义未完成
        r"^\s*@\w+",  # 装饰器
        r"^\s*with\s+.*:?\s*$",  # with语句
        r"^\s*try\s*:\s*$",  # try语句开始
        r"^\s*except\s+.*:\s*$",  # except语句
        r"^\s*finally\s*:\s*$",  # finally语句
        r"^\s*else\s*:\s*$",  # else语句
        r"^\s*elif\s+.*:\s*$",  # elif语句
        r"^\s*if\s+.*:\s*$",  # if语句
        r"^\s*for\s+.*:\s*$",  # for语句
        r"^\s*while\s+.*:\s*$",  # while语句
        r".*\\$",  # 行继续符
        r".*\[\s*$",  # 列表开始
        r".*\{\s*$",  # 字典开始
        r".*\(\s*$",  # 元组或函数调用开始
        r'.*"""\s*$',  # 多行字符串开始
        r".*'''\s*$",  # 多行字符串开始
    ]

    for pattern in python_incomplete_patterns:
        if re.match(pattern, end_line_content):
            return False

    # 检查缩进一致性
    if end_line > 1:
        prev_line = lines[end_line - 2].rstrip()
        if prev_line and end_line_content:
            prev_indent = len(prev_line) - len(prev_line.lstrip())
            curr_indent = len(end_line_content) - len(end_line_content.lstrip())

            # 如果当前行缩进更深，可能在代码块中间
            if curr_indent > prev_indent and not end_line_content.endswith(":"):
                return False

    return True


def is_jsx_boundary_safe(lines: list[str], start_line: int, end_line: int) -> bool:
    """
    检查指定范围是否安全分割，不会破坏JSX标签完整性
    """
    if start_line < 1 or end_line > len(lines):
        return False

    start_idx = start_line - 1
    end_idx = end_line - 1

    if start_idx >= len(lines) or end_idx >= len(lines):
        return False

    start_line_content = lines[start_idx].strip()
    end_line_content = lines[end_idx].strip()

    jsx_continuation_patterns = [
        r"^\s*[a-zA-Z]+=",
        r"^\s*>",
        r"^\s*/>",
        r"^\s*}>",
        r"^\s*\)",
    ]

    for pattern in jsx_continuation_patterns:
        import re

        if re.match(pattern, start_line_content):
            return False

    jsx_incomplete_patterns = [
        r"<[^>]*$",
        r"=\s*$",
        r"=\s*\{[^}]*$",
        r"=\s*\([^)]*$",
    ]

    for pattern in jsx_incomplete_patterns:
        if re.match(pattern, end_line_content):
            return False

    return True


def find_python_safe_split_point(
    lines: list[str],
    start_line: int,
    ideal_end: int,
    max_end: int,
    python_boundaries: list,
) -> int:
    """
    在Python安全的位置寻找分割点，优先考虑Python语法结构的完整性
    """
    search_start = max(start_line, ideal_end - 15)
    search_end = min(max_end, ideal_end + 15)

    best_split = ideal_end
    best_priority = 0

    for line_num in range(search_start, search_end + 1):
        if line_num >= len(lines):
            break

        line = lines[line_num - 1].strip()
        priority = 0

        # Python特有的优先级评分
        if line == "" and line_num < len(lines) - 1:
            next_line = lines[line_num].strip() if line_num < len(lines) else ""
            if next_line.startswith(("def ", "class ", "async def ", "@")):
                priority = 9  # 空行后跟新的定义
            else:
                priority = 6  # 普通空行
        elif line.startswith(("def ", "class ", "async def ")):
            priority = 8  # 在定义之前分割
            best_split = line_num - 1
        elif line.startswith("@"):  # 装饰器
            priority = 7
        elif line.startswith("#"):  # 注释
            priority = 5
        elif line in ["pass", "break", "continue", "return", "raise"]:
            priority = 8  # 控制流语句结束
        elif line.endswith(":") and any(
            line.startswith(kw)
            for kw in [
                "if",
                "for",
                "while",
                "try",
                "except",
                "finally",
                "else",
                "elif",
                "with",
            ]
        ):
            priority = 4  # 控制结构开始，不是最佳分割点

        # 检查是否在Python语法边界内
        is_inside_python_structure = False
        for py_start, py_end, py_type in python_boundaries:
            if py_start < line_num < py_end:
                is_inside_python_structure = True
                break

        if not is_inside_python_structure:
            priority += 2  # 不在语法结构内部的位置更安全

        # 检查Python边界安全性
        if is_python_boundary_safe(lines, start_line, line_num):
            safe_bonus = 2
        else:
            safe_bonus = 0

        total_priority = priority + safe_bonus

        if total_priority > best_priority or (
            total_priority == best_priority
            and abs(line_num - ideal_end) < abs(best_split - ideal_end)
        ):
            best_split = line_num
            best_priority = total_priority

    return best_split


def find_jsx_safe_split_point(
    lines: list[str],
    start_line: int,
    ideal_end: int,
    max_end: int,
    jsx_boundaries: list,
) -> int:
    """
    在JSX安全的位置寻找分割点，优先考虑JSX标签的完整性
    """
    search_start = max(start_line, ideal_end - 15)
    search_end = min(max_end, ideal_end + 15)

    best_split = ideal_end
    best_priority = 0

    for line_num in range(search_start, search_end + 1):
        if line_num >= len(lines):
            break

        line = lines[line_num - 1].strip()
        priority = 0

        is_inside_jsx = False
        for jsx_start, jsx_end, jsx_type in jsx_boundaries:
            if jsx_start < line_num < jsx_end:
                is_inside_jsx = True
                break

        if is_inside_jsx:
            priority = 1
        else:
            is_jsx_boundary = False
            for jsx_start, jsx_end, jsx_type in jsx_boundaries:
                if line_num == jsx_end:
                    priority = 8
                    is_jsx_boundary = True
                    break
                elif line_num == jsx_start - 1:
                    priority = 7
                    is_jsx_boundary = True
                    break

            if not is_jsx_boundary:
                if line == "}" or line in ["})", "};", "},"]:
                    priority = 6
                elif line == "":
                    priority = 5
                elif line.endswith((";", ",")):
                    priority = 4
                elif line.startswith(("// ", "/* ", "* ")):
                    priority = 3

        if is_jsx_boundary_safe(lines, start_line, line_num):
            safe_bonus = 2
        else:
            safe_bonus = 0

        total_priority = priority + safe_bonus

        if total_priority > best_priority or (
            total_priority == best_priority
            and abs(line_num - ideal_end) < abs(best_split - ideal_end)
        ):
            best_split = line_num
            best_priority = total_priority

    return best_split


def split_large_block_python_safe(
    lines: list,
    start_line: int,
    end_line: int,
    target_lines: int,
    max_lines: int,
    python_boundaries: list,
) -> list[tuple[int, int]]:
    """
    Python安全的大块分割，优先保护Python语法结构完整性
    """
    ranges = []
    current_start = start_line

    while current_start <= end_line:
        remaining_lines = end_line - current_start + 1

        if remaining_lines <= max_lines:
            ranges.append((current_start, end_line))
            break

        ideal_end = current_start + target_lines - 1
        actual_end = find_python_safe_split_point(
            lines,
            current_start,
            ideal_end,
            min(current_start + max_lines - 1, end_line),
            python_boundaries,
        )

        ranges.append((current_start, actual_end))
        current_start = actual_end + 1

    return ranges


def split_large_block_jsx_safe(
    lines: list,
    start_line: int,
    end_line: int,
    target_lines: int,
    max_lines: int,
    jsx_boundaries: list,
) -> list[tuple[int, int]]:
    """
    JSX安全的大块分割，优先保护JSX标签完整性
    """
    ranges = []
    current_start = start_line

    while current_start <= end_line:
        remaining_lines = end_line - current_start + 1

        if remaining_lines <= max_lines:
            ranges.append((current_start, end_line))
            break

        ideal_end = current_start + target_lines - 1
        actual_end = find_jsx_safe_split_point(
            lines,
            current_start,
            ideal_end,
            min(current_start + max_lines - 1, end_line),
            jsx_boundaries,
        )

        ranges.append((current_start, actual_end))
        current_start = actual_end + 1

    return ranges


def fix_chunk_boundaries_python_safe(
    ranges: list[tuple[int, int]],
    lines: list[str],
    max_lines: int,
    python_boundaries: list,
) -> list[tuple[int, int]]:
    """
    Python安全的边界修复，确保不会破坏Python语法结构的完整性
    """
    if not ranges or len(ranges) < 2:
        return ranges

    # Python特有的结束符号
    python_ending_symbols = {"pass", "break", "continue", "return", "raise"}

    fixed_ranges = []

    for i, (start, end) in enumerate(ranges):
        if i == 0:
            fixed_ranges.append((start, end))
            continue

        current_start = start
        lines_to_move = 0

        for line_idx in range(start - 1, min(end, start + 10)):
            if line_idx >= len(lines):
                break

            line = lines[line_idx].strip()

            if not line:
                lines_to_move += 1
                continue

            # 检查Python注释
            if line.startswith("#"):
                # 检查注释是否应该与后续代码合并
                should_merge_comment = False
                for check_idx in range(line_idx + 1, min(len(lines), line_idx + 5)):
                    check_line = lines[check_idx].strip()
                    if not check_line:
                        continue
                    if (
                        check_line.startswith(("def ", "class ", "async def ", "@"))
                        or "=" in check_line
                    ):
                        should_merge_comment = True
                        break
                    elif check_line.startswith("#"):
                        continue
                    else:
                        should_merge_comment = True
                        break

                if should_merge_comment:
                    lines_to_move += 1
                else:
                    break
                continue

            # 检查Python结束符号
            is_python_ending = line in python_ending_symbols

            # 检查缩进结束（函数、类、控制结构结束）
            if line_idx > 0:
                prev_line = lines[line_idx - 1].strip()
                if prev_line and line:
                    prev_indent = len(lines[line_idx - 1]) - len(
                        lines[line_idx - 1].lstrip()
                    )
                    curr_indent = len(lines[line_idx]) - len(lines[line_idx].lstrip())
                    # 如果缩进减少，可能是代码块结束
                    if curr_indent < prev_indent:
                        is_python_ending = True

            if is_python_ending:
                lines_to_move += 1
            else:
                break

        if lines_to_move > 0:
            # 检查移动是否安全（不破坏Python语法边界）
            move_line_num = start + lines_to_move
            is_python_safe_to_move = True

            for py_start, py_end, py_type in python_boundaries:
                if py_start <= move_line_num <= py_end:
                    if move_line_num == py_end:
                        is_python_safe_to_move = True
                    else:
                        is_python_safe_to_move = False
                    break

            if is_python_safe_to_move:
                prev_start, prev_end = fixed_ranges[-1]
                new_prev_end = prev_end + lines_to_move
                new_current_start = current_start + lines_to_move

                prev_new_size = new_prev_end - prev_start + 1
                if prev_new_size <= max_lines and new_current_start <= end:
                    fixed_ranges[-1] = (prev_start, new_prev_end)
                    if new_current_start <= end:
                        fixed_ranges.append((new_current_start, end))
                else:
                    fixed_ranges.append((start, end))
            else:
                fixed_ranges.append((start, end))
        else:
            fixed_ranges.append((start, end))

    return fixed_ranges


def fix_chunk_boundaries_jsx_safe(
    ranges: list[tuple[int, int]],
    lines: list[str],
    max_lines: int,
    jsx_boundaries: list,
) -> list[tuple[int, int]]:
    """
    JSX安全的边界修复，确保不会破坏JSX标签的完整性
    """
    if not ranges or len(ranges) < 2:
        return ranges

    ending_symbols = {"}", ")", ",", ";", "};", "},", "})", "});}", "),", ");", "});"}

    fixed_ranges = []

    for i, (start, end) in enumerate(ranges):
        if i == 0:
            fixed_ranges.append((start, end))
            continue

        current_start = start
        lines_to_move = 0

        for line_idx in range(start - 1, min(end, start + 10)):
            if line_idx >= len(lines):
                break

            line = lines[line_idx].strip()

            if not line:
                lines_to_move += 1
                continue

            # 检查是否是单行注释（应该与后续代码合并）
            if line.startswith("//") or line.startswith("#"):
                # 检查这个注释是否应该与后面的代码合并
                should_merge_comment = False

                # 查看注释后面几行是否有相关代码
                for check_idx in range(line_idx + 1, min(len(lines), line_idx + 5)):
                    check_line = lines[check_idx].strip()
                    if not check_line:
                        continue
                    # 如果注释后面紧跟着函数、变量定义等，应该合并
                    if (
                        check_line.startswith(
                            (
                                "function",
                                "const",
                                "let",
                                "var",
                                "export",
                                "class",
                                "def",
                            )
                        )
                        or "=" in check_line
                        or check_line.endswith("{")
                    ):
                        should_merge_comment = True
                        break
                    # 如果遇到其他注释，继续检查
                    elif check_line.startswith(("//", "#", "/*")):
                        continue
                    else:
                        # 遇到其他代码，也应该合并
                        should_merge_comment = True
                        break

                if should_merge_comment:
                    lines_to_move += 1
                else:
                    # 不应该合并，停止移动
                    break
                continue

            is_ending_line = False

            if line in ending_symbols:
                is_ending_line = True
            elif all(c in "}),;" or c.isspace() for c in line):
                is_ending_line = True
            elif line.startswith(("}", ")", ",", ";")) and (
                len(line) <= 3 or line.endswith((",", ";", "}", ")"))
            ):
                is_ending_line = True

            if is_ending_line:
                move_line_num = start + lines_to_move
                is_jsx_safe_to_move = True

                for jsx_start, jsx_end, jsx_type in jsx_boundaries:
                    if jsx_start <= move_line_num <= jsx_end:
                        if move_line_num == jsx_end:
                            is_jsx_safe_to_move = True
                        else:
                            is_jsx_safe_to_move = False
                        break

                if is_jsx_safe_to_move:
                    lines_to_move += 1
                else:
                    break
            else:
                break

        if lines_to_move > 0:
            prev_start, prev_end = fixed_ranges[-1]
            new_prev_end = prev_end + lines_to_move
            new_current_start = current_start + lines_to_move

            prev_new_size = new_prev_end - prev_start + 1
            if prev_new_size <= max_lines and new_current_start <= end:
                if is_jsx_boundary_safe(
                    lines, prev_start, new_prev_end
                ) and is_jsx_boundary_safe(lines, new_current_start, end):
                    fixed_ranges[-1] = (prev_start, new_prev_end)
                    if new_current_start <= end:
                        fixed_ranges.append((new_current_start, end))
                else:
                    fixed_ranges.append((start, end))
            else:
                fixed_ranges.append((start, end))
        else:
            fixed_ranges.append((start, end))

    return fixed_ranges


def is_jsx_attribute_safe_split(lines: list[str], split_line: int) -> bool:
    """
    检查在指定行分割是否会破坏JSX属性的完整性
    同时保护React Hooks（useCallback、useMemo等）的完整性
    """
    if split_line < 1 or split_line >= len(lines):
        return True

    check_start = max(0, split_line - 5)
    check_end = min(len(lines), split_line + 5)

    for check_idx in range(check_start, check_end):
        line = lines[check_idx].strip()

        if check_idx <= split_line:
            import re

            hook_patterns = [
                r"const\s+\w+\s*=\s*useCallback\s*\(",
                r"const\s+\w+\s*=\s*useMemo\s*\(",
                r"const\s+\w+\s*=\s*useEffect\s*\(",
                r"const\s+\w+\s*=\s*useState\s*\(",
                r"const\s+\w+\s*=\s*useRef\s*\(",
            ]

            for pattern in hook_patterns:
                if re.search(pattern, line):
                    hook_end_found = False
                    paren_count = 0
                    in_hook = False

                    for search_idx in range(check_idx, min(len(lines), check_idx + 20)):
                        search_line = lines[search_idx]

                        for char in search_line:
                            if char == "(":
                                paren_count += 1
                                in_hook = True
                            elif char == ")":
                                paren_count -= 1
                                if in_hook and paren_count == 0:
                                    remaining = search_line[search_line.find(")") :]
                                    if (
                                        ", [])" in remaining
                                        or remaining.strip().endswith(", [])")
                                        or "), [])" in remaining
                                        or remaining.strip().endswith("), [])")
                                    ):
                                        hook_end_found = True
                                        if search_idx > split_line:
                                            return False
                                        break

                        if hook_end_found:
                            break

                    if not hook_end_found and check_idx <= split_line:
                        return False

        if ".map(" in line and check_idx <= split_line:
            for search_idx in range(check_idx, min(len(lines), check_idx + 15)):
                search_line = lines[search_idx].strip()
                if "})) ||" in search_line or search_line.endswith("}}) || []}"):
                    if search_idx > split_line:
                        return False
                    break

        jsx_incomplete_patterns = [
            r"=\s*\{[^}]*\.map\s*\([^)]*=>\s*\([^)]*$",
            r"=>\s*\(\s*\{[^}]*$",
            r"[a-zA-Z]+\s*=\s*\{[^}]*$",
        ]

        for pattern in jsx_incomplete_patterns:
            if re.search(pattern, line) and check_idx <= split_line:
                return False

    return True


def fix_python_syntax_boundaries(
    snippets: list[Snippet], code: str, path: str
) -> list[Snippet]:
    """
    后处理：修复有Python语法截断问题的分块
    """
    if not snippets:
        return snippets

    lines = code.split("\n")
    fixed_snippets = []
    i = 0

    while i < len(snippets):
        snippet = snippets[i]

        if not is_python_boundary_safe(lines, snippet.start, snippet.end):
            if i < len(snippets) - 1:
                next_snippet = snippets[i + 1]
                merged = Snippet(
                    content=code,
                    start=snippet.start,
                    end=next_snippet.end,
                    file_path=path,
                )
                if hasattr(snippet, "metadata"):
                    merged.metadata = snippet.metadata
                fixed_snippets.append(merged)
                i += 2
                continue
            else:
                # 最后一个snippet，尝试找到安全的结束点
                safe_end = snippet.end
                for test_end in range(
                    snippet.end + 1, min(len(lines) + 1, snippet.end + 20)
                ):
                    if is_python_boundary_safe(lines, snippet.start, test_end):
                        safe_end = test_end
                        break

                adjusted = Snippet(
                    content=code,
                    start=snippet.start,
                    end=safe_end,
                    file_path=path,
                )
                if hasattr(snippet, "metadata"):
                    adjusted.metadata = snippet.metadata
                fixed_snippets.append(adjusted)
                i += 1
                continue

        fixed_snippets.append(snippet)
        i += 1

    return fixed_snippets


def fix_jsx_attribute_boundaries(
    snippets: list[Snippet], code: str, path: str
) -> list[Snippet]:
    """
    后处理：修复有JSX属性截断问题的分块
    """
    if not snippets:
        return snippets

    lines = code.split("\n")
    fixed_snippets = []
    i = 0

    while i < len(snippets):
        snippet = snippets[i]

        if not is_jsx_attribute_safe_split(lines, snippet.end):
            if i < len(snippets) - 1:
                next_snippet = snippets[i + 1]
                merged = Snippet(
                    content=code,
                    start=snippet.start,
                    end=next_snippet.end,
                    file_path=path,
                )
                if hasattr(snippet, "metadata"):
                    merged.metadata = snippet.metadata
                fixed_snippets.append(merged)
                i += 2
                continue
            else:
                safe_end = snippet.end
                for test_end in range(
                    snippet.end + 1, min(len(lines) + 1, snippet.end + 20)
                ):
                    if is_jsx_attribute_safe_split(lines, test_end):
                        safe_end = test_end
                        break

                adjusted = Snippet(
                    content=code,
                    start=snippet.start,
                    end=safe_end,
                    file_path=path,
                )
                if hasattr(snippet, "metadata"):
                    adjusted.metadata = snippet.metadata
                fixed_snippets.append(adjusted)
                i += 1
                continue

        fixed_snippets.append(snippet)
        i += 1

    return fixed_snippets


def chunk_css_with_tree_sitter(
    code: str,
    path: str,
    target_lines: int = DEFAULT_TARGET_LINES,
    min_lines: int = MIN_CHUNK_LINES,
    max_lines: int = MAX_CHUNK_LINES,
) -> list[Snippet]:
    """
    使用tree-sitter进行CSS文件的智能分块
    基于CSS AST进行语义分块，保持CSS规则的完整性
    """
    lines = code.split("\n")
    total_lines = len(lines)

    # 如果文件很小，直接返回整个文件
    if total_lines <= max_lines:
        return [
            Snippet(
                content=code,
                start=1,
                end=total_lines,
                file_path=path,
            )
        ]

    try:
        # 使用tree-sitter解析CSS
        parser = get_parser("css")
        tree = parser.parse(code.encode("utf-8"))

        # 获取CSS规则节点
        css_rules = get_css_rule_nodes(tree, code)

        if css_rules:
            # 基于CSS规则进行分块
            ranges = create_css_rule_ranges(
                css_rules, lines, target_lines, min_lines, max_lines
            )
        else:
            # 没有找到CSS规则，使用基本分块
            ranges = create_basic_ranges(lines, target_lines, max_lines)

        # 使用JSX/TSX相同的后处理流程
        ranges = coalesce_small_chunks(ranges, min_lines, max_lines)
        ranges = fix_chunk_boundaries(ranges, lines, max_lines)

        # 转换为Snippet对象
        snippets = []
        for start, end in ranges:
            snippet = Snippet(
                content=code,
                start=start,
                end=end,
                file_path=path,
            )
            snippets.append(snippet)

        return filter_empty_snippets(snippets)

    except Exception as e:
        logger.warning(f"Tree-sitter CSS parsing failed for {path}: {e}")
        # 回退到原始的CSS分块方法
        return chunk_css(code, path, target_lines, min_lines, max_lines)


def get_css_rule_nodes(tree, code: str) -> list[dict]:
    """
    从CSS AST中提取规则节点
    """
    rules = []

    def traverse_css(node):
        # CSS规则类型
        if node.type in [
            "rule_set",
            "at_rule",
            "media_statement",
            "keyframes_statement",
        ]:
            start_line = node.start_point[0] + 1
            end_line = node.end_point[0] + 1

            rules.append(
                {
                    "type": node.type,
                    "start_line": start_line,
                    "end_line": end_line,
                    "start_byte": node.start_byte,
                    "end_byte": node.end_byte,
                }
            )

        # 递归处理子节点
        for child in node.children:
            traverse_css(child)

    traverse_css(tree.root_node)
    return rules


def create_css_rule_ranges(
    css_rules: list[dict],
    lines: list[str],
    target_lines: int,
    min_lines: int,
    max_lines: int,
) -> list[tuple[int, int]]:
    """
    基于CSS规则创建分块范围
    """
    ranges = []
    current_start = 1
    current_rules = []

    # 按起始行排序
    css_rules.sort(key=lambda x: x["start_line"])

    for rule in css_rules:
        rule_start = rule["start_line"]
        rule_end = rule["end_line"]

        # 处理规则前的空隙
        if current_start < rule_start:
            gap_size = rule_start - current_start
            if gap_size >= min_lines:
                # 空隙足够大，单独成块
                ranges.append((current_start, rule_start - 1))
            elif current_rules:
                # 空隙很小，与当前规则组合并
                current_rules[-1]["end_line"] = rule_start - 1

        # 添加当前规则到组合中
        if not current_rules:
            current_rules = [rule]
        else:
            # 检查是否应该开始新的组合
            combined_size = rule_end - current_rules[0]["start_line"] + 1
            if combined_size > max_lines:
                # 当前组合已经太大，结束当前组合
                group_start = current_rules[0]["start_line"]
                group_end = current_rules[-1]["end_line"]
                ranges.append((group_start, group_end))
                current_rules = [rule]
            else:
                current_rules.append(rule)

        current_start = rule_end + 1

    # 处理最后的规则组合
    if current_rules:
        group_start = current_rules[0]["start_line"]
        group_end = current_rules[-1]["end_line"]
        ranges.append((group_start, group_end))

    # 处理文件末尾的剩余内容
    if current_start <= len(lines):
        remaining_size = len(lines) - current_start + 1
        if remaining_size >= min_lines:
            ranges.append((current_start, len(lines)))
        elif ranges:
            # 剩余内容很少，与最后一个块合并
            ranges[-1] = (ranges[-1][0], len(lines))

    return ranges


def create_basic_ranges(
    lines: list[str], target_lines: int, max_lines: int
) -> list[tuple[int, int]]:
    """
    创建基本的分块范围（当无法使用AST时的回退方案）
    """
    ranges = []
    total_lines = len(lines)
    current_start = 1

    while current_start <= total_lines:
        current_end = min(current_start + target_lines - 1, total_lines)

        # 寻找合适的分割点
        if current_end < total_lines:
            # 在目标位置附近寻找空行或注释
            for i in range(
                max(current_start, current_end - 5), min(total_lines, current_end + 5)
            ):
                line = lines[i - 1].strip()  # 转换为0-based索引
                if not line or line.startswith("/*") or line.startswith("//"):
                    current_end = i
                    break

        ranges.append((current_start, current_end))
        current_start = current_end + 1

    return ranges


def chunk_json_with_tree_sitter(
    code: str,
    path: str,
    target_lines: int = DEFAULT_TARGET_LINES,
    min_lines: int = MIN_CHUNK_LINES,
    max_lines: int = MAX_CHUNK_LINES,
) -> list[Snippet]:
    """
    使用tree-sitter进行JSON文件的智能分块
    基于JSON AST进行语义分块，保持JSON结构的完整性
    """
    lines = code.split("\n")
    total_lines = len(lines)

    # 如果文件很小，直接返回整个文件
    if total_lines <= max_lines:
        return [
            Snippet(
                content=code,
                start=1,
                end=total_lines,
                file_path=path,
            )
        ]

    try:
        # 使用tree-sitter解析JSON
        parser = get_parser("json")
        tree = parser.parse(code.encode("utf-8"))

        # 获取JSON结构节点
        json_nodes = get_json_structure_nodes(tree, code)

        if json_nodes:
            # 基于JSON结构进行分块
            ranges = create_json_structure_ranges(
                json_nodes, lines, target_lines, min_lines, max_lines
            )
        else:
            # 没有找到JSON结构，使用基本分块
            ranges = create_basic_ranges(lines, target_lines, max_lines)

        # 使用JSX/TSX相同的后处理流程
        ranges = coalesce_small_chunks(ranges, min_lines, max_lines)
        ranges = fix_chunk_boundaries(ranges, lines, max_lines)

        # 转换为Snippet对象
        snippets = []
        for start, end in ranges:
            snippet = Snippet(
                content=code,
                start=start,
                end=end,
                file_path=path,
            )
            snippets.append(snippet)

        return filter_empty_snippets(snippets)

    except Exception as e:
        logger.warning(f"Tree-sitter JSON parsing failed for {path}: {e}")
        # 回退到原始的JSON分块方法
        return chunk_json(code, path, target_lines, min_lines, max_lines)


def get_json_structure_nodes(tree, code: str) -> list[dict]:
    """
    从JSON AST中提取结构节点
    """
    nodes = []

    def traverse_json(node):
        # JSON结构类型
        if node.type in ["object", "array", "pair"]:
            start_line = node.start_point[0] + 1
            end_line = node.end_point[0] + 1

            # 只处理跨越多行的结构
            if end_line > start_line:
                nodes.append(
                    {
                        "type": node.type,
                        "start_line": start_line,
                        "end_line": end_line,
                        "start_byte": node.start_byte,
                        "end_byte": node.end_byte,
                    }
                )

        # 递归处理子节点
        for child in node.children:
            traverse_json(child)

    traverse_json(tree.root_node)
    return nodes


def create_json_structure_ranges(
    json_nodes: list[dict],
    lines: list[str],
    target_lines: int,
    min_lines: int,
    max_lines: int,
) -> list[tuple[int, int]]:
    """
    基于JSON结构创建分块范围
    """
    ranges = []
    total_lines = len(lines)

    # 按起始行排序，并过滤嵌套的节点
    json_nodes.sort(key=lambda x: (x["start_line"], -x["end_line"]))
    filtered_nodes = []

    for node in json_nodes:
        # 检查是否与已有节点重叠
        is_nested = False
        for existing in filtered_nodes:
            if (
                existing["start_line"] <= node["start_line"]
                and existing["end_line"] >= node["end_line"]
            ):
                is_nested = True
                break

        if not is_nested:
            filtered_nodes.append(node)

    if not filtered_nodes:
        return create_basic_ranges(lines, target_lines, max_lines)

    current_start = 1

    for node in filtered_nodes:
        node_start = node["start_line"]
        node_end = node["end_line"]

        # 处理节点前的内容
        if current_start < node_start:
            gap_size = node_start - current_start
            if gap_size >= min_lines:
                ranges.append((current_start, node_start - 1))

        # 处理当前节点
        node_size = node_end - node_start + 1
        if node_size <= max_lines:
            # 节点大小合适，直接作为一个块
            ranges.append((node_start, node_end))
        else:
            # 节点太大，需要进一步分割
            sub_ranges = split_large_json_node(node, lines, target_lines, max_lines)
            ranges.extend(sub_ranges)

        current_start = node_end + 1

    # 处理文件末尾的剩余内容
    if current_start <= total_lines:
        remaining_size = total_lines - current_start + 1
        if remaining_size >= min_lines:
            ranges.append((current_start, total_lines))
        elif ranges:
            # 剩余内容很少，与最后一个块合并
            ranges[-1] = (ranges[-1][0], total_lines)

    return ranges


def split_large_json_node(
    node: dict, lines: list[str], target_lines: int, max_lines: int
) -> list[tuple[int, int]]:
    """
    分割过大的JSON节点
    """
    ranges = []
    start_line = node["start_line"]
    end_line = node["end_line"]
    current_start = start_line

    while current_start <= end_line:
        current_end = min(current_start + target_lines - 1, end_line)

        # 寻找合适的分割点（逗号、大括号、方括号）
        if current_end < end_line:
            for i in range(current_end, min(end_line, current_end + 10)):
                line = lines[i - 1].strip()  # 转换为0-based索引
                if line.endswith(",") or line in ["}", "]", "{", "["]:
                    current_end = i
                    break

        ranges.append((current_start, current_end))
        current_start = current_end + 1

    return ranges


def chunk_html_with_tree_sitter(
    code: str,
    path: str,
    target_lines: int = DEFAULT_TARGET_LINES,
    min_lines: int = MIN_CHUNK_LINES,
    max_lines: int = MAX_CHUNK_LINES,
) -> list[Snippet]:
    """
    使用tree-sitter进行HTML文件的智能分块
    基于HTML AST进行语义分块，保持HTML标签结构的完整性
    """
    lines = code.split("\n")
    total_lines = len(lines)

    # 如果文件很小，直接返回整个文件
    if total_lines <= max_lines:
        return [
            Snippet(
                content=code,
                start=1,
                end=total_lines,
                file_path=path,
            )
        ]

    try:
        # 使用tree-sitter解析HTML
        parser = get_parser("html")
        tree = parser.parse(code.encode("utf-8"))

        # 获取HTML元素节点
        html_elements = get_html_element_nodes(tree, code)

        if html_elements:
            # 基于HTML元素进行分块
            ranges = create_html_element_ranges(
                html_elements, lines, target_lines, min_lines, max_lines
            )
        else:
            # 没有找到HTML元素，使用基本分块
            ranges = create_basic_ranges(lines, target_lines, max_lines)

        # 使用JSX/TSX相同的后处理流程
        ranges = coalesce_small_chunks(ranges, min_lines, max_lines)
        ranges = fix_chunk_boundaries(ranges, lines, max_lines)

        # 转换为Snippet对象
        snippets = []
        for start, end in ranges:
            snippet = Snippet(
                content=code,
                start=start,
                end=end,
                file_path=path,
            )
            snippets.append(snippet)

        return filter_empty_snippets(snippets)

    except Exception as e:
        logger.warning(f"Tree-sitter HTML parsing failed for {path}: {e}")
        # 回退到原始的HTML分块方法
        return chunk_html(code, path, target_lines, min_lines, max_lines)


def get_html_element_nodes(tree, code: str) -> list[dict]:
    """
    从HTML AST中提取元素节点
    """
    elements = []

    def traverse_html(node):
        # HTML元素类型
        if node.type in ["element", "script_element", "style_element", "doctype"]:
            start_line = node.start_point[0] + 1
            end_line = node.end_point[0] + 1

            # 获取标签名
            tag_name = ""
            if node.type == "element":
                start_tag = node.child_by_field_name("start_tag")
                if start_tag:
                    tag_name_node = start_tag.child_by_field_name("name")
                    if tag_name_node:
                        tag_name = tag_name_node.text.decode("utf-8")
            elif node.type in ["script_element", "style_element"]:
                tag_name = node.type.replace("_element", "")
            elif node.type == "doctype":
                tag_name = "doctype"

            elements.append(
                {
                    "type": node.type,
                    "tag_name": tag_name,
                    "start_line": start_line,
                    "end_line": end_line,
                    "start_byte": node.start_byte,
                    "end_byte": node.end_byte,
                }
            )

        # 递归处理子节点
        for child in node.children:
            traverse_html(child)

    traverse_html(tree.root_node)
    return elements


def create_html_element_ranges(
    html_elements: list[dict],
    lines: list[str],
    target_lines: int,
    min_lines: int,
    max_lines: int,
) -> list[tuple[int, int]]:
    """
    基于HTML元素创建分块范围
    """
    ranges = []
    total_lines = len(lines)

    # 按起始行排序，并过滤嵌套的元素
    html_elements.sort(key=lambda x: (x["start_line"], -x["end_line"]))
    filtered_elements = []

    for element in html_elements:
        # 检查是否与已有元素重叠（嵌套）
        is_nested = False
        for existing in filtered_elements:
            if (
                existing["start_line"] <= element["start_line"]
                and existing["end_line"] >= element["end_line"]
                and existing != element
            ):
                is_nested = True
                break

        if not is_nested:
            filtered_elements.append(element)

    if not filtered_elements:
        return create_basic_ranges(lines, target_lines, max_lines)

    current_start = 1

    for element in filtered_elements:
        element_start = element["start_line"]
        element_end = element["end_line"]

        # 处理元素前的内容
        if current_start < element_start:
            gap_size = element_start - current_start
            if gap_size >= min_lines:
                ranges.append((current_start, element_start - 1))

        # 处理当前元素
        element_size = element_end - element_start + 1

        # 特殊处理script和style元素，保持完整性
        if element["tag_name"] in ["script", "style"]:
            if element_size <= max_lines:
                ranges.append((element_start, element_end))
            else:
                # 即使很大也尽量保持完整，但可以在合适的地方分割
                sub_ranges = split_large_html_element(
                    element, lines, target_lines, max_lines
                )
                ranges.extend(sub_ranges)
        else:
            if element_size <= max_lines:
                ranges.append((element_start, element_end))
            else:
                # 元素太大，需要进一步分割
                sub_ranges = split_large_html_element(
                    element, lines, target_lines, max_lines
                )
                ranges.extend(sub_ranges)

        current_start = element_end + 1

    # 处理文件末尾的剩余内容
    if current_start <= total_lines:
        remaining_size = total_lines - current_start + 1
        if remaining_size >= min_lines:
            ranges.append((current_start, total_lines))
        elif ranges:
            # 剩余内容很少，与最后一个块合并
            ranges[-1] = (ranges[-1][0], total_lines)

    return ranges


def split_large_html_element(
    element: dict, lines: list[str], target_lines: int, max_lines: int
) -> list[tuple[int, int]]:
    """
    分割过大的HTML元素
    """
    ranges = []
    start_line = element["start_line"]
    end_line = element["end_line"]
    current_start = start_line

    # 对于script和style元素，尽量保持完整性
    if element["tag_name"] in ["script", "style"]:
        # 尝试找到函数或规则边界
        while current_start <= end_line:
            current_end = min(current_start + max_lines - 1, end_line)

            # 寻找合适的分割点
            if current_end < end_line:
                # 寻找函数结束、空行等
                for i in range(current_end, min(end_line, current_end + 10)):
                    line = lines[i - 1].strip()  # 转换为0-based索引
                    if (
                        not line
                        or line.endswith("}")
                        or line.endswith(";")
                        or line.startswith("//")
                        or line.startswith("/*")
                    ):
                        current_end = i
                        break

            ranges.append((current_start, current_end))
            current_start = current_end + 1
    else:
        # 普通HTML元素，按标签边界分割
        while current_start <= end_line:
            current_end = min(current_start + target_lines - 1, end_line)

            # 寻找合适的分割点（标签结束）
            if current_end < end_line:
                for i in range(current_end, min(end_line, current_end + 10)):
                    line = lines[i - 1].strip()  # 转换为0-based索引
                    if line.startswith("</") or line.endswith(">") or not line:
                        current_end = i
                        break

            ranges.append((current_start, current_end))
            current_start = current_end + 1

    return ranges
