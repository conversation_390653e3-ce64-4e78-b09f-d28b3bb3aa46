#!/usr/bin/env python3
"""
CodeMind 边界情况测试脚本

专门测试发现的问题和边界情况：
1. 守护进程稳定性测试
2. 高频搜索压力测试
3. 大文件索引性能测试
4. 多客户端并发测试
"""

import os
import sys
import time
import threading
import multiprocessing
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from codemind_api import CodeMindClient


class EdgeCaseTester:
    """边界情况测试器"""
    
    def __init__(self, repo_path: str):
        self.repo_path = repo_path
        
    def test_daemon_stability(self, duration_minutes: int = 5):
        """测试守护进程稳定性"""
        print(f"🔧 开始守护进程稳定性测试 (持续 {duration_minutes} 分钟)...")
        
        client = CodeMindClient(self.repo_path)
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        
        success_count = 0
        failure_count = 0
        restart_count = 0
        
        while time.time() < end_time:
            try:
                # 执行搜索测试守护进程是否正常
                result = client.codebase_search("test", limit=1)
                if result.get('success', False):
                    success_count += 1
                else:
                    failure_count += 1
                    if 'restart' in str(result).lower():
                        restart_count += 1
                        
                # 随机间隔，模拟真实使用场景
                time.sleep(0.5 + (time.time() % 2))
                
            except Exception as e:
                failure_count += 1
                print(f"  ❌ 守护进程异常: {str(e)}")
                time.sleep(1)
                
        total_requests = success_count + failure_count
        print(f"  📊 稳定性测试结果:")
        print(f"    总请求数: {total_requests}")
        print(f"    成功次数: {success_count}")
        print(f"    失败次数: {failure_count}")
        print(f"    重启次数: {restart_count}")
        print(f"    成功率: {success_count/total_requests:.2%}")
        
        return {
            'total_requests': total_requests,
            'success_count': success_count,
            'failure_count': failure_count,
            'restart_count': restart_count,
            'success_rate': success_count/total_requests if total_requests > 0 else 0
        }
        
    def test_high_frequency_search(self, requests_per_second: int = 10, duration_seconds: int = 30):
        """测试高频搜索"""
        print(f"⚡ 开始高频搜索测试 ({requests_per_second} 请求/秒, 持续 {duration_seconds} 秒)...")
        
        client = CodeMindClient(self.repo_path)
        queries = ["function", "class", "import", "def", "return", "if", "for", "while"]
        
        results = []
        start_time = time.time()
        request_interval = 1.0 / requests_per_second
        
        def single_search():
            query = queries[int(time.time()) % len(queries)]
            search_start = time.time()
            try:
                result = client.codebase_search(query, limit=3)
                search_time = time.time() - search_start
                return {
                    'success': result.get('success', False),
                    'time': search_time,
                    'query': query
                }
            except Exception as e:
                search_time = time.time() - search_start
                return {
                    'success': False,
                    'time': search_time,
                    'error': str(e),
                    'query': query
                }
        
        # 高频发送请求
        next_request_time = start_time
        while time.time() - start_time < duration_seconds:
            if time.time() >= next_request_time:
                result = single_search()
                results.append(result)
                next_request_time += request_interval
            else:
                time.sleep(0.01)  # 短暂休眠
                
        # 统计结果
        total_requests = len(results)
        successful_requests = sum(1 for r in results if r['success'])
        avg_response_time = sum(r['time'] for r in results) / total_requests if total_requests > 0 else 0
        actual_rps = total_requests / duration_seconds
        
        print(f"  📊 高频搜索测试结果:")
        print(f"    目标频率: {requests_per_second} 请求/秒")
        print(f"    实际频率: {actual_rps:.2f} 请求/秒")
        print(f"    总请求数: {total_requests}")
        print(f"    成功次数: {successful_requests}")
        print(f"    成功率: {successful_requests/total_requests:.2%}")
        print(f"    平均响应时间: {avg_response_time:.3f}s")
        
        return {
            'target_rps': requests_per_second,
            'actual_rps': actual_rps,
            'total_requests': total_requests,
            'successful_requests': successful_requests,
            'success_rate': successful_requests/total_requests if total_requests > 0 else 0,
            'avg_response_time': avg_response_time
        }
        
    def test_large_file_indexing(self, file_size_mb: int = 10, num_files: int = 5):
        """测试大文件索引性能"""
        print(f"📁 开始大文件索引测试 ({num_files} 个文件, 每个 {file_size_mb}MB)...")
        
        # 创建大文件
        large_files = []
        for i in range(num_files):
            file_path = os.path.join(self.repo_path, f"large_test_file_{i}.py")
            
            # 生成大文件内容
            content_lines = []
            lines_needed = (file_size_mb * 1024 * 1024) // 100  # 假设每行约100字节
            
            for line_num in range(lines_needed):
                if line_num % 1000 == 0:
                    content_lines.append(f"# Section {line_num // 1000}")
                content_lines.append(f"def function_{line_num}(param_{line_num}):")
                content_lines.append(f"    return param_{line_num} * {line_num}")
                content_lines.append("")
                
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content_lines))
            large_files.append(file_path)
            
        print(f"  ✅ 创建了 {num_files} 个大文件")
        
        # 测试索引性能
        client = CodeMindClient(self.repo_path)
        
        print("  📚 开始索引大文件...")
        index_start = time.time()
        index_result = client.index_codebase(force_reindex=True)
        index_time = time.time() - index_start
        
        # 计算文件大小
        total_size_mb = sum(os.path.getsize(f) for f in large_files) / (1024 * 1024)
        
        print(f"  📊 大文件索引结果:")
        print(f"    索引成功: {index_result.get('success', False)}")
        print(f"    总文件大小: {total_size_mb:.2f}MB")
        print(f"    索引耗时: {index_time:.2f}s")
        print(f"    索引速度: {total_size_mb/index_time:.2f}MB/s")
        
        # 清理大文件
        for file_path in large_files:
            try:
                os.remove(file_path)
            except:
                pass
                
        return {
            'success': index_result.get('success', False),
            'total_size_mb': total_size_mb,
            'index_time': index_time,
            'index_speed_mb_per_sec': total_size_mb/index_time if index_time > 0 else 0
        }
        
    def test_multi_client_concurrent(self, num_processes: int = 3, searches_per_process: int = 10):
        """测试多客户端并发访问"""
        print(f"🚀 开始多客户端并发测试 ({num_processes} 进程, 每进程 {searches_per_process} 次搜索)...")
        
        def worker_process(process_id: int):
            """工作进程函数"""
            client = CodeMindClient(self.repo_path)
            queries = ["function", "class", "import", "def", "return"]
            
            process_results = []
            for i in range(searches_per_process):
                query = queries[i % len(queries)]
                start_time = time.time()
                try:
                    result = client.codebase_search(query, limit=3)
                    search_time = time.time() - start_time
                    process_results.append({
                        'process_id': process_id,
                        'search_index': i,
                        'success': result.get('success', False),
                        'time': search_time,
                        'query': query
                    })
                except Exception as e:
                    search_time = time.time() - start_time
                    process_results.append({
                        'process_id': process_id,
                        'search_index': i,
                        'success': False,
                        'time': search_time,
                        'error': str(e),
                        'query': query
                    })
                    
            return process_results
            
        # 使用进程池执行并发测试
        start_time = time.time()
        with ProcessPoolExecutor(max_workers=num_processes) as executor:
            futures = [executor.submit(worker_process, i) for i in range(num_processes)]
            all_results = []
            
            for future in futures:
                try:
                    process_results = future.result()
                    all_results.extend(process_results)
                except Exception as e:
                    print(f"  ❌ 进程执行失败: {str(e)}")
                    
        total_time = time.time() - start_time
        
        # 统计结果
        total_searches = len(all_results)
        successful_searches = sum(1 for r in all_results if r['success'])
        avg_time = sum(r['time'] for r in all_results) / total_searches if total_searches > 0 else 0
        
        print(f"  📊 多客户端并发测试结果:")
        print(f"    总搜索次数: {total_searches}")
        print(f"    成功次数: {successful_searches}")
        print(f"    成功率: {successful_searches/total_searches:.2%}")
        print(f"    总耗时: {total_time:.2f}s")
        print(f"    平均搜索时间: {avg_time:.3f}s")
        print(f"    整体吞吐量: {total_searches/total_time:.2f} 次/秒")
        
        return {
            'total_searches': total_searches,
            'successful_searches': successful_searches,
            'success_rate': successful_searches/total_searches if total_searches > 0 else 0,
            'total_time': total_time,
            'avg_search_time': avg_time,
            'throughput': total_searches/total_time if total_time > 0 else 0
        }


def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='CodeMind 边界情况测试')
    parser.add_argument('--repo-path', required=True, help='测试仓库路径')
    parser.add_argument('--test', choices=['stability', 'frequency', 'large-files', 'multi-client', 'all'], 
                       default='all', help='测试类型')
    
    args = parser.parse_args()
    
    tester = EdgeCaseTester(args.repo_path)
    
    if args.test == 'all':
        print("🧪 运行所有边界情况测试...\n")
        
        # 守护进程稳定性测试
        tester.test_daemon_stability(duration_minutes=2)
        print()
        
        # 高频搜索测试
        tester.test_high_frequency_search(requests_per_second=5, duration_seconds=20)
        print()
        
        # 大文件索引测试
        tester.test_large_file_indexing(file_size_mb=5, num_files=3)
        print()
        
        # 多客户端并发测试
        tester.test_multi_client_concurrent(num_processes=3, searches_per_process=5)
        
    elif args.test == 'stability':
        tester.test_daemon_stability()
    elif args.test == 'frequency':
        tester.test_high_frequency_search()
    elif args.test == 'large-files':
        tester.test_large_file_indexing()
    elif args.test == 'multi-client':
        tester.test_multi_client_concurrent()


if __name__ == "__main__":
    main()
