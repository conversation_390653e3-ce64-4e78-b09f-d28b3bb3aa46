#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 vector_db.py 的修复
"""

import numpy as np
from loguru import logger
import sys
import os

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

from core.vector_db import (
    openai_call_embedding,
    openai_call_embedding_router,
    openai_with_expo_backoff,
    embed_text_array,
    normalize_l2
)

def test_empty_batch():
    """测试空批次的处理"""
    logger.info("测试空批次的处理")
    result = openai_call_embedding([])
    assert isinstance(result, np.ndarray)
    assert result.shape == (0, 512)
    logger.info("空批次测试通过")

def test_single_document():
    """测试单个文档的嵌入"""
    logger.info("测试单个文档的嵌入")
    text = "这是一个测试文档"
    result = openai_call_embedding([text])
    assert isinstance(result, np.ndarray)
    assert result.shape[0] == 1
    assert result.shape[1] > 0
    logger.info(f"单个文档嵌入形状: {result.shape}")
    logger.info("单个文档测试通过")

def test_multiple_documents():
    """测试多个文档的嵌入"""
    logger.info("测试多个文档的嵌入")
    texts = ["文档一", "文档二", "文档三"]
    result = openai_call_embedding(texts)
    assert isinstance(result, np.ndarray)
    assert result.shape[0] == 3
    assert result.shape[1] > 0
    logger.info(f"多个文档嵌入形状: {result.shape}")
    logger.info("多个文档测试通过")

def test_long_document():
    """测试长文档的截断"""
    logger.info("测试长文档的截断")
    # 创建一个超过 8192 tokens 的文档
    long_text = "测试 " * 10000
    result = openai_call_embedding([long_text])
    assert isinstance(result, np.ndarray)
    assert result.shape[0] == 1
    assert result.shape[1] > 0
    logger.info(f"长文档嵌入形状: {result.shape}")
    logger.info("长文档测试通过")

def test_embedding_cache():
    """测试嵌入缓存"""
    logger.info("测试嵌入缓存")
    text = "缓存测试文档"
    
    # 第一次调用，应该会访问嵌入服务
    logger.info("第一次调用嵌入函数")
    result1 = openai_with_expo_backoff([text])
    
    # 第二次调用，应该会使用缓存
    logger.info("第二次调用嵌入函数，应该使用缓存")
    result2 = openai_with_expo_backoff([text])
    
    assert isinstance(result1, np.ndarray)
    assert isinstance(result2, np.ndarray)
    assert np.array_equal(result1, result2)
    logger.info("缓存测试通过")

def test_normalize_l2():
    """测试 L2 归一化"""
    logger.info("测试 L2 归一化")
    vectors = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]])
    normalized = normalize_l2(vectors)
    
    # 检查每个向量的 L2 范数是否约等于 1
    norms = np.linalg.norm(normalized, axis=1)
    assert np.allclose(norms, np.ones_like(norms))
    logger.info("L2 归一化测试通过")

def main():
    """运行所有测试"""
    logger.info("开始测试 vector_db.py 的修复")
    
    try:
        test_empty_batch()
        test_normalize_l2()
        test_single_document()
        test_multiple_documents()
        test_long_document()
        test_embedding_cache()
        
        logger.info("所有测试通过！")
    except Exception as e:
        logger.error(f"测试失败: {e}")
        raise

if __name__ == "__main__":
    main() 