#!/usr/bin/env python3
"""
CodeMind 压力测试脚本

测试功能：
1. 索引和搜索基本功能
2. 文件修改后的增量索引
3. 文件删除后的搜索验证
4. 并发搜索压力测试
5. 大量文件修改的索引性能测试

使用方法：
python stress_test_codemind.py --repo-path /path/to/test/repo [--test-type all|basic|incremental|concurrent|performance]
"""

import os
import sys
import time
import json
import shutil
import random
import string
import argparse
import threading
import tempfile
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path

# 导入CodeMind API
from codemind_api import CodeMindClient


class CodeMindStressTester:
    """CodeMind压力测试器"""
    
    def __init__(self, repo_path: str):
        self.repo_path = repo_path
        self.client = CodeMindClient(repo_path)
        self.test_files = []
        self.original_files_backup = {}
        
    def setup_test_environment(self):
        """设置测试环境"""
        print("🔧 设置测试环境...")
        
        # 确保仓库路径存在
        if not os.path.exists(self.repo_path):
            os.makedirs(self.repo_path)
            
        # 创建一些测试文件
        self._create_test_files()
        
        # 初始索引
        print("📚 创建初始索引...")
        result = self.client.index_codebase(force_reindex=True)
        if not result.get('success', False):
            raise Exception(f"初始索引失败: {result}")
        print("✅ 初始索引完成")
        
    def _create_test_files(self):
        """创建测试文件"""
        test_files_data = [
            {
                'path': 'src/utils/helper.py',
                'content': '''def calculate_sum(a, b):
    """计算两个数的和"""
    return a + b

def calculate_product(a, b):
    """计算两个数的乘积"""
    return a * b

class MathHelper:
    """数学辅助类"""
    
    def __init__(self):
        self.name = "MathHelper"
        
    def power(self, base, exponent):
        """计算幂"""
        return base ** exponent
'''
            },
            {
                'path': 'src/models/user.py',
                'content': '''class User:
    """用户模型类"""
    
    def __init__(self, name, email):
        self.name = name
        self.email = email
        self.id = None
        
    def save(self):
        """保存用户"""
        print(f"Saving user: {self.name}")
        
    def delete(self):
        """删除用户"""
        print(f"Deleting user: {self.name}")

class UserManager:
    """用户管理器"""
    
    def __init__(self):
        self.users = []
        
    def add_user(self, user):
        """添加用户"""
        self.users.append(user)
        
    def find_user_by_email(self, email):
        """根据邮箱查找用户"""
        for user in self.users:
            if user.email == email:
                return user
        return None
'''
            },
            {
                'path': 'src/services/api.py',
                'content': '''import requests
from typing import Dict, Any

class APIService:
    """API服务类"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = requests.Session()
        
    def get(self, endpoint: str) -> Dict[str, Any]:
        """GET请求"""
        url = f"{self.base_url}/{endpoint}"
        response = self.session.get(url)
        return response.json()
        
    def post(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """POST请求"""
        url = f"{self.base_url}/{endpoint}"
        response = self.session.post(url, json=data)
        return response.json()

def create_api_client(base_url: str) -> APIService:
    """创建API客户端"""
    return APIService(base_url)
'''
            },
            {
                'path': 'tests/test_helper.py',
                'content': '''import unittest
from src.utils.helper import calculate_sum, calculate_product, MathHelper

class TestMathHelper(unittest.TestCase):
    """测试数学辅助函数"""
    
    def test_calculate_sum(self):
        """测试求和函数"""
        self.assertEqual(calculate_sum(2, 3), 5)
        self.assertEqual(calculate_sum(-1, 1), 0)
        
    def test_calculate_product(self):
        """测试乘积函数"""
        self.assertEqual(calculate_product(2, 3), 6)
        self.assertEqual(calculate_product(0, 5), 0)
        
    def test_math_helper_power(self):
        """测试幂函数"""
        helper = MathHelper()
        self.assertEqual(helper.power(2, 3), 8)
        self.assertEqual(helper.power(5, 0), 1)

if __name__ == '__main__':
    unittest.main()
'''
            }
        ]
        
        for file_data in test_files_data:
            file_path = os.path.join(self.repo_path, file_data['path'])
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(file_data['content'])
            
            self.test_files.append(file_path)
            # 备份原始内容
            self.original_files_backup[file_path] = file_data['content']
            
        print(f"✅ 创建了 {len(test_files_data)} 个测试文件")
        
    def test_basic_functionality(self) -> Dict[str, Any]:
        """测试基本功能"""
        print("\n🧪 开始基本功能测试...")
        results = {}
        
        # 测试搜索功能
        test_queries = [
            "calculate_sum",
            "User",
            "APIService",
            "MathHelper",
            "test_calculate"
        ]
        
        for query in test_queries:
            print(f"🔍 搜索: {query}")
            start_time = time.time()
            result = self.client.codebase_search(query, limit=10)
            search_time = time.time() - start_time
            
            if result.get('success', False):
                # 解析搜索结果
                if 'message' in result:
                    # 从message中提取结果数量
                    message = result['message']
                    if '搜索结果数量:' in message:
                        count_line = [line for line in message.split('\n') if '搜索结果数量:' in line][0]
                        result_count = int(count_line.split(':')[1].strip())
                    else:
                        result_count = 0
                else:
                    result_count = 0
                    
                results[query] = {
                    'success': True,
                    'count': result_count,
                    'time': search_time
                }
                print(f"  ✅ 找到 {result_count} 个结果，耗时 {search_time:.2f}s")
            else:
                results[query] = {
                    'success': False,
                    'error': result.get('error', 'Unknown error'),
                    'time': search_time
                }
                print(f"  ❌ 搜索失败: {result.get('error', 'Unknown error')}")
                
        return results
        
    def test_incremental_indexing(self) -> Dict[str, Any]:
        """测试增量索引"""
        print("\n🔄 开始增量索引测试...")
        results = {}
        
        # 修改一个文件
        test_file = self.test_files[0]  # src/utils/helper.py
        print(f"📝 修改文件: {test_file}")
        
        # 添加新函数
        new_content = self.original_files_backup[test_file] + '''

def calculate_difference(a, b):
    """计算两个数的差"""
    return a - b

def calculate_division(a, b):
    """计算两个数的商"""
    if b == 0:
        raise ValueError("除数不能为零")
    return a / b
'''
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
            
        # 触发增量索引
        print("📚 触发增量索引...")
        start_time = time.time()
        index_result = self.client.index_codebase()
        index_time = time.time() - start_time
        
        results['indexing'] = {
            'success': index_result.get('success', False),
            'time': index_time
        }
        
        if index_result.get('success', False):
            print(f"  ✅ 增量索引完成，耗时 {index_time:.2f}s")
            
            # 测试新添加的函数是否能被搜索到
            print("🔍 测试新函数搜索...")
            search_result = self.client.codebase_search("calculate_difference", limit=5)
            
            if search_result.get('success', False):
                # 检查是否找到新函数
                message = search_result.get('message', '')
                if '搜索结果数量:' in message:
                    count_line = [line for line in message.split('\n') if '搜索结果数量:' in line][0]
                    result_count = int(count_line.split(':')[1].strip())
                    
                    results['new_function_search'] = {
                        'success': result_count > 0,
                        'count': result_count
                    }
                    
                    if result_count > 0:
                        print(f"  ✅ 新函数搜索成功，找到 {result_count} 个结果")
                    else:
                        print("  ❌ 新函数搜索失败，未找到结果")
                else:
                    results['new_function_search'] = {'success': False, 'error': 'Cannot parse result count'}
                    print("  ❌ 无法解析搜索结果")
            else:
                results['new_function_search'] = {'success': False, 'error': search_result.get('error', 'Search failed')}
                print(f"  ❌ 新函数搜索失败: {search_result.get('error', 'Unknown error')}")
        else:
            print(f"  ❌ 增量索引失败: {index_result.get('error', 'Unknown error')}")
            
        return results

    def test_file_deletion(self) -> Dict[str, Any]:
        """测试文件删除后的搜索验证"""
        print("\n🗑️ 开始文件删除测试...")
        results = {}

        # 选择一个测试文件进行删除
        file_to_delete = self.test_files[1]  # src/models/user.py
        print(f"🗑️ 删除文件: {file_to_delete}")

        # 先搜索确认文件内容存在
        print("🔍 删除前搜索验证...")
        before_result = self.client.codebase_search("UserManager", limit=5)
        before_found = False
        if before_result.get('success', False):
            message = before_result.get('message', '')
            if '搜索结果数量:' in message:
                count_line = [line for line in message.split('\n') if '搜索结果数量:' in line][0]
                result_count = int(count_line.split(':')[1].strip())
                before_found = result_count > 0

        results['before_deletion'] = {'found': before_found}
        print(f"  删除前搜索结果: {'找到' if before_found else '未找到'}")

        # 删除文件
        if os.path.exists(file_to_delete):
            os.remove(file_to_delete)
            print(f"  ✅ 文件已删除: {file_to_delete}")

            # 触发重新索引
            print("📚 触发重新索引...")
            start_time = time.time()
            index_result = self.client.index_codebase()
            index_time = time.time() - start_time

            results['reindexing'] = {
                'success': index_result.get('success', False),
                'time': index_time
            }

            if index_result.get('success', False):
                print(f"  ✅ 重新索引完成，耗时 {index_time:.2f}s")

                # 搜索验证文件内容不再存在
                print("🔍 删除后搜索验证...")
                after_result = self.client.codebase_search("UserManager", limit=5)
                after_found = False
                if after_result.get('success', False):
                    message = after_result.get('message', '')
                    if '搜索结果数量:' in message:
                        count_line = [line for line in message.split('\n') if '搜索结果数量:' in line][0]
                        result_count = int(count_line.split(':')[1].strip())
                        after_found = result_count > 0

                results['after_deletion'] = {'found': after_found}

                if not after_found and before_found:
                    print("  ✅ 文件删除验证成功：删除前能找到，删除后找不到")
                    results['deletion_verified'] = True
                elif not before_found:
                    print("  ⚠️ 删除前就找不到内容，无法验证删除效果")
                    results['deletion_verified'] = False
                else:
                    print("  ❌ 文件删除验证失败：删除后仍能找到内容")
                    results['deletion_verified'] = False
            else:
                print(f"  ❌ 重新索引失败: {index_result.get('error', 'Unknown error')}")
                results['deletion_verified'] = False
        else:
            print(f"  ❌ 文件不存在: {file_to_delete}")
            results['deletion_verified'] = False

        return results

    def test_concurrent_search(self, num_threads: int = 5, searches_per_thread: int = 10) -> Dict[str, Any]:
        """测试并发搜索"""
        print(f"\n🚀 开始并发搜索测试 ({num_threads} 线程, 每线程 {searches_per_thread} 次搜索)...")
        results = {}

        queries = [
            "calculate_sum", "User", "APIService", "MathHelper", "test_calculate",
            "helper", "model", "service", "function", "class"
        ]

        def worker_search(thread_id: int) -> Dict[str, Any]:
            """工作线程搜索函数"""
            thread_results = []
            for i in range(searches_per_thread):
                query = random.choice(queries)
                start_time = time.time()
                try:
                    result = self.client.codebase_search(query, limit=5)
                    search_time = time.time() - start_time

                    success = result.get('success', False)
                    thread_results.append({
                        'query': query,
                        'success': success,
                        'time': search_time,
                        'thread_id': thread_id,
                        'search_index': i
                    })

                except Exception as e:
                    search_time = time.time() - start_time
                    thread_results.append({
                        'query': query,
                        'success': False,
                        'time': search_time,
                        'error': str(e),
                        'thread_id': thread_id,
                        'search_index': i
                    })

            return thread_results

        # 执行并发搜索
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(worker_search, i) for i in range(num_threads)]
            all_results = []

            for future in as_completed(futures):
                try:
                    thread_results = future.result()
                    all_results.extend(thread_results)
                except Exception as e:
                    print(f"  ❌ 线程执行失败: {str(e)}")

        total_time = time.time() - start_time

        # 统计结果
        total_searches = len(all_results)
        successful_searches = sum(1 for r in all_results if r['success'])
        failed_searches = total_searches - successful_searches
        avg_time = sum(r['time'] for r in all_results) / total_searches if total_searches > 0 else 0

        results = {
            'total_searches': total_searches,
            'successful_searches': successful_searches,
            'failed_searches': failed_searches,
            'success_rate': successful_searches / total_searches if total_searches > 0 else 0,
            'total_time': total_time,
            'avg_search_time': avg_time,
            'searches_per_second': total_searches / total_time if total_time > 0 else 0
        }

        print(f"  📊 并发搜索统计:")
        print(f"    总搜索次数: {total_searches}")
        print(f"    成功次数: {successful_searches}")
        print(f"    失败次数: {failed_searches}")
        print(f"    成功率: {results['success_rate']:.2%}")
        print(f"    总耗时: {total_time:.2f}s")
        print(f"    平均搜索时间: {avg_time:.3f}s")
        print(f"    搜索吞吐量: {results['searches_per_second']:.2f} 次/秒")

        return results

    def test_performance_with_many_files(self, num_files: int = 50) -> Dict[str, Any]:
        """测试大量文件的索引性能"""
        print(f"\n⚡ 开始性能测试 (创建 {num_files} 个文件)...")
        results = {}

        # 创建大量测试文件
        temp_files = []
        for i in range(num_files):
            file_path = os.path.join(self.repo_path, f"perf_test/file_{i:03d}.py")
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            content = f'''# Performance test file {i}

class TestClass{i}:
    """测试类 {i}"""

    def __init__(self):
        self.id = {i}
        self.name = "test_{i}"

    def method_{i}(self, param):
        """测试方法 {i}"""
        return param * {i}

    def calculate_{i}(self, x, y):
        """计算函数 {i}"""
        return x + y + {i}

def function_{i}(data):
    """全局函数 {i}"""
    return data + {i}

# 常量定义
CONSTANT_{i} = {i * 100}
CONFIG_{i} = {{
    "value": {i},
    "name": "config_{i}",
    "enabled": {str(i % 2 == 0).lower()}
}}
'''

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            temp_files.append(file_path)

        print(f"  ✅ 创建了 {num_files} 个测试文件")

        # 测试索引性能
        print("📚 开始索引性能测试...")
        start_time = time.time()
        index_result = self.client.index_codebase(force_reindex=True)
        index_time = time.time() - start_time

        results['indexing'] = {
            'success': index_result.get('success', False),
            'time': index_time,
            'files_per_second': num_files / index_time if index_time > 0 else 0
        }

        if index_result.get('success', False):
            print(f"  ✅ 索引完成，耗时 {index_time:.2f}s")
            print(f"  📊 索引速度: {results['indexing']['files_per_second']:.2f} 文件/秒")

            # 测试搜索性能
            print("🔍 开始搜索性能测试...")
            search_queries = [f"TestClass{i}" for i in range(0, min(num_files, 10), 2)]
            search_results = []

            for query in search_queries:
                start_time = time.time()
                search_result = self.client.codebase_search(query, limit=5)
                search_time = time.time() - start_time

                search_results.append({
                    'query': query,
                    'success': search_result.get('success', False),
                    'time': search_time
                })

            avg_search_time = sum(r['time'] for r in search_results) / len(search_results)
            successful_searches = sum(1 for r in search_results if r['success'])

            results['searching'] = {
                'total_queries': len(search_queries),
                'successful_queries': successful_searches,
                'avg_search_time': avg_search_time,
                'success_rate': successful_searches / len(search_queries)
            }

            print(f"  📊 搜索统计:")
            print(f"    查询次数: {len(search_queries)}")
            print(f"    成功次数: {successful_searches}")
            print(f"    平均搜索时间: {avg_search_time:.3f}s")
            print(f"    成功率: {results['searching']['success_rate']:.2%}")
        else:
            print(f"  ❌ 索引失败: {index_result.get('error', 'Unknown error')}")

        # 清理测试文件
        print("🧹 清理测试文件...")
        for file_path in temp_files:
            try:
                os.remove(file_path)
            except:
                pass
        try:
            os.rmdir(os.path.join(self.repo_path, "perf_test"))
        except:
            pass

        return results

    def cleanup_test_environment(self):
        """清理测试环境"""
        print("\n🧹 清理测试环境...")

        # 恢复被修改的文件
        for file_path, original_content in self.original_files_backup.items():
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(original_content)
                    print(f"  ✅ 恢复文件: {file_path}")
                except Exception as e:
                    print(f"  ❌ 恢复文件失败 {file_path}: {str(e)}")

        # 恢复被删除的文件
        for file_path in self.test_files:
            if not os.path.exists(file_path) and file_path in self.original_files_backup:
                try:
                    os.makedirs(os.path.dirname(file_path), exist_ok=True)
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(self.original_files_backup[file_path])
                    print(f"  ✅ 恢复被删除的文件: {file_path}")
                except Exception as e:
                    print(f"  ❌ 恢复被删除文件失败 {file_path}: {str(e)}")

        print("✅ 测试环境清理完成")

    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        print("🚀 开始 CodeMind 压力测试")
        print("=" * 60)

        all_results = {}

        try:
            # 设置测试环境
            self.setup_test_environment()

            # 基本功能测试
            all_results['basic'] = self.test_basic_functionality()

            # 增量索引测试
            all_results['incremental'] = self.test_incremental_indexing()

            # 文件删除测试
            all_results['deletion'] = self.test_file_deletion()

            # 并发搜索测试
            all_results['concurrent'] = self.test_concurrent_search()

            # 性能测试
            all_results['performance'] = self.test_performance_with_many_files()

        except Exception as e:
            print(f"\n❌ 测试过程中发生错误: {str(e)}")
            all_results['error'] = str(e)

        finally:
            # 清理测试环境
            self.cleanup_test_environment()

        return all_results

    def print_summary(self, results: Dict[str, Any]):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("📊 测试总结")
        print("=" * 60)

        if 'error' in results:
            print(f"❌ 测试失败: {results['error']}")
            return

        # 基本功能测试总结
        if 'basic' in results:
            basic = results['basic']
            successful_queries = sum(1 for r in basic.values() if r.get('success', False))
            total_queries = len(basic)
            print(f"🧪 基本功能测试: {successful_queries}/{total_queries} 查询成功")

        # 增量索引测试总结
        if 'incremental' in results:
            incremental = results['incremental']
            indexing_success = incremental.get('indexing', {}).get('success', False)
            search_success = incremental.get('new_function_search', {}).get('success', False)
            print(f"🔄 增量索引测试: 索引{'成功' if indexing_success else '失败'}, 搜索{'成功' if search_success else '失败'}")

        # 文件删除测试总结
        if 'deletion' in results:
            deletion = results['deletion']
            deletion_verified = deletion.get('deletion_verified', False)
            print(f"🗑️ 文件删除测试: {'验证成功' if deletion_verified else '验证失败'}")

        # 并发搜索测试总结
        if 'concurrent' in results:
            concurrent = results['concurrent']
            success_rate = concurrent.get('success_rate', 0)
            searches_per_second = concurrent.get('searches_per_second', 0)
            print(f"🚀 并发搜索测试: 成功率 {success_rate:.1%}, 吞吐量 {searches_per_second:.1f} 次/秒")

        # 性能测试总结
        if 'performance' in results:
            performance = results['performance']
            indexing = performance.get('indexing', {})
            searching = performance.get('searching', {})
            files_per_second = indexing.get('files_per_second', 0)
            avg_search_time = searching.get('avg_search_time', 0)
            print(f"⚡ 性能测试: 索引速度 {files_per_second:.1f} 文件/秒, 平均搜索时间 {avg_search_time:.3f}s")

        print("=" * 60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='CodeMind 压力测试脚本')
    parser.add_argument('--repo-path', required=True, help='测试仓库路径')
    parser.add_argument('--test-type', choices=['all', 'basic', 'incremental', 'deletion', 'concurrent', 'performance'],
                       default='all', help='测试类型')
    parser.add_argument('--threads', type=int, default=5, help='并发测试线程数')
    parser.add_argument('--searches-per-thread', type=int, default=10, help='每线程搜索次数')
    parser.add_argument('--perf-files', type=int, default=50, help='性能测试文件数量')

    args = parser.parse_args()

    # 检查仓库路径
    if not os.path.exists(args.repo_path):
        print(f"❌ 仓库路径不存在: {args.repo_path}")
        sys.exit(1)

    # 创建测试器
    tester = CodeMindStressTester(args.repo_path)

    try:
        if args.test_type == 'all':
            # 运行所有测试
            results = tester.run_all_tests()
            tester.print_summary(results)

        else:
            # 运行单个测试
            tester.setup_test_environment()

            if args.test_type == 'basic':
                results = tester.test_basic_functionality()
                print(f"\n📊 基本功能测试结果: {json.dumps(results, indent=2, ensure_ascii=False)}")

            elif args.test_type == 'incremental':
                results = tester.test_incremental_indexing()
                print(f"\n📊 增量索引测试结果: {json.dumps(results, indent=2, ensure_ascii=False)}")

            elif args.test_type == 'deletion':
                results = tester.test_file_deletion()
                print(f"\n📊 文件删除测试结果: {json.dumps(results, indent=2, ensure_ascii=False)}")

            elif args.test_type == 'concurrent':
                results = tester.test_concurrent_search(args.threads, args.searches_per_thread)
                print(f"\n📊 并发搜索测试结果: {json.dumps(results, indent=2, ensure_ascii=False)}")

            elif args.test_type == 'performance':
                results = tester.test_performance_with_many_files(args.perf_files)
                print(f"\n📊 性能测试结果: {json.dumps(results, indent=2, ensure_ascii=False)}")

            tester.cleanup_test_environment()

    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        tester.cleanup_test_environment()
        sys.exit(1)

    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        tester.cleanup_test_environment()
        sys.exit(1)


if __name__ == "__main__":
    main()
