{"name": "@xhs/codemind", "version": "1.0.17", "description": "codemind sdk", "main": "dist/mcp/index.js", "module": "dist/mcp/index.js", "types": "dist/mcp/index.d.ts", "type": "module", "exports": {".": {"import": "./dist/mcp/index.js", "types": "./dist/mcp/index.d.ts"}}, "scripts": {"test": "test", "clean": "rm -rf dist", "install:mcp": "cd mcp && npm install", "build:mcp": "cd mcp && npm run build", "copy:mcp": "mkdir -p dist/mcp && cp -r mcp/build/* dist/mcp/ && cp -f index.d.ts dist/ || true", "setup:python": "python3 -m venv .venv && . .venv/bin/activate && pip3 install -r requirements.txt", "inspect:mcp": "npx @modelcontextprotocol/inspector node ./dist/mcp/index.js", "build": "npm run clean && npm run install:mcp && npm run build:mcp && npm run copy:mcp", "prepublishOnly": "npm run build"}, "files": ["dist", "python_dist"], "repository": {"type": "git", "url": "codemind"}, "keywords": ["codemind"], "author": "codemind", "license": "ISC", "devDependencies": {"typescript": "^5.7.2"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.11.2", "axios": "^1.9.0", "uuid": "^11.1.0"}}