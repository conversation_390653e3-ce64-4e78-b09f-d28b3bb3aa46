# CodeMind 压力测试脚本使用指南

## 概述

`stress_test_codemind.py` 是一个全面的 CodeMind 压力测试脚本，用于验证 CodeMind 的索引和搜索功能在各种场景下的表现。

## 测试功能

### 1. 基本功能测试 (basic)
- 测试基本的代码库搜索功能
- 验证不同查询词的搜索结果
- 检查搜索响应时间

### 2. 增量索引测试 (incremental)
- 修改文件内容后触发增量索引
- 验证新添加的代码能被正确索引和搜索
- 测试索引更新的性能

### 3. 文件删除测试 (deletion)
- 删除文件后重新索引
- 验证被删除文件的内容不再出现在搜索结果中
- 确保索引的一致性

### 4. 并发搜索测试 (concurrent)
- 多线程并发执行搜索请求
- 测试系统在高并发下的稳定性
- 统计搜索成功率和吞吐量

### 5. 性能测试 (performance)
- 创建大量文件测试索引性能
- 测试大规模代码库的搜索性能
- 统计索引和搜索的速度指标

## 使用方法

### 运行所有测试
```bash
python stress_test_codemind.py --repo-path /path/to/test/repo
```

### 运行特定测试
```bash
# 基本功能测试
python stress_test_codemind.py --repo-path /path/to/test/repo --test-type basic

# 增量索引测试
python stress_test_codemind.py --repo-path /path/to/test/repo --test-type incremental

# 文件删除测试
python stress_test_codemind.py --repo-path /path/to/test/repo --test-type deletion

# 并发搜索测试（5个线程，每线程10次搜索）
python stress_test_codemind.py --repo-path /path/to/test/repo --test-type concurrent --threads 5 --searches-per-thread 10

# 性能测试（创建50个文件）
python stress_test_codemind.py --repo-path /path/to/test/repo --test-type performance --perf-files 50
```

### 参数说明

- `--repo-path`: 测试仓库路径（必需）
- `--test-type`: 测试类型，可选值：all, basic, incremental, deletion, concurrent, performance
- `--threads`: 并发测试的线程数（默认5）
- `--searches-per-thread`: 每个线程执行的搜索次数（默认10）
- `--perf-files`: 性能测试中创建的文件数量（默认50）

## 测试流程

### 自动测试环境设置
1. 脚本会在指定的仓库路径下创建测试文件
2. 自动触发初始索引
3. 执行各项测试
4. 自动清理测试环境，恢复原始状态

### 测试文件结构
脚本会创建以下测试文件：
```
src/
├── utils/
│   └── helper.py          # 数学辅助函数
├── models/
│   └── user.py           # 用户模型类
└── services/
    └── api.py            # API服务类
tests/
└── test_helper.py        # 单元测试
```

## 输出示例

### 测试过程输出
```
🚀 开始 CodeMind 压力测试
============================================================
🔧 设置测试环境...
✅ 创建了 4 个测试文件
📚 创建初始索引...
✅ 初始索引完成

🧪 开始基本功能测试...
🔍 搜索: calculate_sum
  ✅ 找到 1 个结果，耗时 0.15s
🔍 搜索: User
  ✅ 找到 2 个结果，耗时 0.12s
...

🔄 开始增量索引测试...
📝 修改文件: /path/to/repo/src/utils/helper.py
📚 触发增量索引...
  ✅ 增量索引完成，耗时 0.08s
🔍 测试新函数搜索...
  ✅ 新函数搜索成功，找到 1 个结果
...
```

### 测试总结
```
============================================================
📊 测试总结
============================================================
🧪 基本功能测试: 5/5 查询成功
🔄 增量索引测试: 索引成功, 搜索成功
🗑️ 文件删除测试: 验证成功
🚀 并发搜索测试: 成功率 100.0%, 吞吐量 8.5 次/秒
⚡ 性能测试: 索引速度 12.5 文件/秒, 平均搜索时间 0.089s
============================================================
```

## 注意事项

1. **测试路径**: 确保指定的仓库路径存在且有写入权限
2. **环境清理**: 脚本会自动清理测试环境，恢复原始文件状态
3. **中断处理**: 支持 Ctrl+C 中断，会自动执行清理操作
4. **依赖检查**: 确保 CodeMind API 正常工作
5. **资源使用**: 性能测试可能会消耗较多系统资源

## 故障排除

### 常见问题

1. **索引失败**: 检查 CodeMind 服务是否正常运行
2. **搜索无结果**: 确认索引已完成且文件内容正确
3. **并发测试失败**: 可能是系统资源不足，尝试减少线程数
4. **权限错误**: 确保对测试路径有读写权限

### 调试建议

1. 先运行基本功能测试确认环境正常
2. 查看 CodeMind 日志了解详细错误信息
3. 逐步增加测试复杂度定位问题
