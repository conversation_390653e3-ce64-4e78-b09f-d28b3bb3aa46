class User:
    """用户模型类"""
    
    def __init__(self, name, email):
        self.name = name
        self.email = email
        self.id = None
        
    def save(self):
        """保存用户"""
        print(f"Saving user: {self.name}")
        
    def delete(self):
        """删除用户"""
        print(f"Deleting user: {self.name}")

class UserManager:
    """用户管理器"""
    
    def __init__(self):
        self.users = []
        
    def add_user(self, user):
        """添加用户"""
        self.users.append(user)
        
    def find_user_by_email(self, email):
        """根据邮箱查找用户"""
        for user in self.users:
            if user.email == email:
                return user
        return None
