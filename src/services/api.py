import requests
from typing import Dict, Any

class APIService:
    """API服务类"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = requests.Session()
        
    def get(self, endpoint: str) -> Dict[str, Any]:
        """GET请求"""
        url = f"{self.base_url}/{endpoint}"
        response = self.session.get(url)
        return response.json()
        
    def post(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """POST请求"""
        url = f"{self.base_url}/{endpoint}"
        response = self.session.post(url, json=data)
        return response.json()

def create_api_client(base_url: str) -> APIService:
    """创建API客户端"""
    return APIService(base_url)
