import unittest
from src.utils.helper import calculate_sum, calculate_product, MathHelper

class TestMathHelper(unittest.TestCase):
    """测试数学辅助函数"""
    
    def test_calculate_sum(self):
        """测试求和函数"""
        self.assertEqual(calculate_sum(2, 3), 5)
        self.assertEqual(calculate_sum(-1, 1), 0)
        
    def test_calculate_product(self):
        """测试乘积函数"""
        self.assertEqual(calculate_product(2, 3), 6)
        self.assertEqual(calculate_product(0, 5), 0)
        
    def test_math_helper_power(self):
        """测试幂函数"""
        helper = MathHelper()
        self.assertEqual(helper.power(2, 3), 8)
        self.assertEqual(helper.power(5, 0), 1)

if __name__ == '__main__':
    unittest.main()
